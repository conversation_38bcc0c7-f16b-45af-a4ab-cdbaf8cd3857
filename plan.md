### white_level_calibrator.h
```cpp
#pragma once

#include <cstddef>
#include <cstdint>
#include <vector>
#include "../../../types.h"

namespace IQVideoProcessor::Pipeline {

struct WhiteLevelCalibratorSettings {
  float topFraction;
  std::size_t minTopSampleCount;
  VSampleFloat minDynamicRange;
  VSampleFloat initialWhiteLevel;
  float attackPercentPerSecond;
  float releasePercentPerSecond;
};

class WhiteLevelCalibrator {
public:
  explicit WhiteLevelCalibrator(const WhiteLevelCalibratorSettings& settings);

  void reset();
  void setNominalFrameRate(float frameRate);
  void setExpectedLineCount(std::size_t lineCount);

  void beginFrame(VSampleFloat blackReference);
  void accumulateLinePeak(const VSampleFloat* samples, uint32_t sampleCount);
  void finalizeFrame();

  VSampleFloat currentWhiteLevel() const;

private:
  float computeSmoothingAlpha(float percentPerSecond) const;
  VSampleFloat applyBounds(VSampleFloat candidate) const;

  const WhiteLevelCalibratorSettings settings_;
  float nominalFrameRate_{25.0f};
  VSampleFloat blackReference_{0.0f};
  VSampleFloat filteredWhiteLevel_;
  std::vector<VSampleFloat> linePeaks_;
};

} // namespace IQVideoProcessor::Pipeline
```

### white_level_calibrator.cpp
```cpp
#include "./white_level_calibrator.h"
#include <algorithm>
#include <cmath>

namespace IQVideoProcessor::Pipeline {

namespace {
constexpr VSampleFloat MAX_WHITE_LEVEL = static_cast<VSampleFloat>(M_PI);
constexpr float FALLBACK_FRAME_RATE = 25.0f;

inline std::size_t ceilToSizeT(float value) {
  if (value <= 0.0f) return 0;
  const auto truncated = static_cast<std::size_t>(value);
  return (value > static_cast<float>(truncated)) ? truncated + 1 : truncated;
}
} // namespace

WhiteLevelCalibrator::WhiteLevelCalibrator(const WhiteLevelCalibratorSettings& settings)
  : settings_(settings),
    filteredWhiteLevel_(settings.initialWhiteLevel) {}

void WhiteLevelCalibrator::reset() {
  linePeaks_.clear();
  blackReference_ = 0.0f;
  filteredWhiteLevel_ = settings_.initialWhiteLevel;
  nominalFrameRate_ = FALLBACK_FRAME_RATE;
}

void WhiteLevelCalibrator::setNominalFrameRate(const float frameRate) {
  nominalFrameRate_ = frameRate > 0.0f ? frameRate : FALLBACK_FRAME_RATE;
}

void WhiteLevelCalibrator::setExpectedLineCount(const std::size_t lineCount) {
  if (lineCount > linePeaks_.capacity()) {
    linePeaks_.reserve(lineCount);
  }
}

void WhiteLevelCalibrator::beginFrame(const VSampleFloat blackReference) {
  blackReference_ = blackReference;
  linePeaks_.clear();
  filteredWhiteLevel_ = applyBounds(filteredWhiteLevel_);
}

void WhiteLevelCalibrator::accumulateLinePeak(const VSampleFloat* samples, const uint32_t sampleCount) {
  if (samples == nullptr || sampleCount == 0U) return;

  VSampleFloat peak = blackReference_;
  for (uint32_t i = 0; i < sampleCount; ++i) {
    const VSampleFloat sample = samples[i];
    if (!std::isfinite(sample)) continue;
    if (sample > peak) {
      peak = sample;
    }
  }

  linePeaks_.push_back(peak);
}

void WhiteLevelCalibrator::finalizeFrame() {
  if (!linePeaks_.empty()) {
    auto removeIt = std::remove_if(
      linePeaks_.begin(),
      linePeaks_.end(),
      [](const VSampleFloat value) { return !std::isfinite(value); }
    );
    linePeaks_.erase(removeIt, linePeaks_.end());
  }

  VSampleFloat candidate = filteredWhiteLevel_;
  if (!linePeaks_.empty()) {
    std::sort(linePeaks_.begin(), linePeaks_.end());

    const std::size_t count = linePeaks_.size();
    const float scaledTop = settings_.topFraction * static_cast<float>(count);
    std::size_t topCount = ceilToSizeT(scaledTop);

    if (topCount < settings_.minTopSampleCount) {
      topCount = settings_.minTopSampleCount;
    }
    if (topCount == 0) {
      topCount = 1;
    }
    if (topCount > count) {
      topCount = count;
    }

    const std::size_t startIndex = count - topCount;
    float sum = 0.0f;
    for (std::size_t i = startIndex; i < count; ++i) {
      sum += linePeaks_[i];
    }
    const float average = sum / static_cast<float>(topCount);
    candidate = static_cast<VSampleFloat>(average);
  }

  candidate = applyBounds(candidate);
  const VSampleFloat delta = candidate - filteredWhiteLevel_;
  const float percent = delta >= 0.0f ? settings_.attackPercentPerSecond : settings_.releasePercentPerSecond;
  const float alpha = computeSmoothingAlpha(percent);
  filteredWhiteLevel_ = static_cast<VSampleFloat>(filteredWhiteLevel_ + alpha * delta);
  filteredWhiteLevel_ = applyBounds(filteredWhiteLevel_);

  linePeaks_.clear();
}

VSampleFloat WhiteLevelCalibrator::currentWhiteLevel() const {
  return applyBounds(filteredWhiteLevel_);
}

float WhiteLevelCalibrator::computeSmoothingAlpha(const float percentPerSecond) const {
  const float rate = std::max(percentPerSecond, 0.0f) * 0.01f;
  if (rate <= 0.0f) return 0.0f;

  const float frameRate = nominalFrameRate_ > 0.0f ? nominalFrameRate_ : FALLBACK_FRAME_RATE;
  const float frameInterval = 1.0f / frameRate;
  const float exponent = -rate * frameInterval;
  const float attenuation = std::expf(exponent);
  float alpha = 1.0f - attenuation;
  if (alpha < 0.0f) alpha = 0.0f;
  if (alpha > 1.0f) alpha = 1.0f;
  return alpha;
}

VSampleFloat WhiteLevelCalibrator::applyBounds(const VSampleFloat candidate) const {
  VSampleFloat bounded = std::isfinite(candidate) ? candidate : MAX_WHITE_LEVEL;
  const VSampleFloat minWhite = blackReference_ + settings_.minDynamicRange;

  if (bounded < minWhite) {
    bounded = minWhite;
  }
  if (bounded > MAX_WHITE_LEVEL) {
    bounded = MAX_WHITE_LEVEL;
  }
  return bounded;
}

} // namespace IQVideoProcessor::Pipeline
```

### frame_composition_node.h
```cpp
#pragma once

#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../line_detection_node_types.h"
#include "../frame_composition_node_types.h"
#include "./partials/frame_canvas.h"
#include "./partials/white_level_calibrator.h"
#include <memory>
#include <optional>

namespace IQVideoProcessor::Pipeline {

class FrameCompositionNode final : public SPipeline::StreamNode<LineDetectionEvent, FrameCompositionResult> {
public:
  explicit FrameCompositionNode(SampleRate sampleRate);
  ~FrameCompositionNode() override;

private:
  bool process(LineDetectionEvent& event) override;

  void handleStandardDetected(VideoStandard standard);
  void handleSyncLock();
  void handleSyncLockLost();
  void handleFrameFieldBegin(uint32_t frameFieldNumber, bool isTopField);
  void handleFrameFieldEnd(uint32_t frameFieldNumber, bool isTopField);
  void handleLineReceived(const std::vector<VSampleFloat>& data, uint32_t dataSize, uint32_t lineNumber, bool isTopField);
  void handleEqualization(const std::vector<VSampleFloat>& data, uint32_t dataSize);
  void handleUnknownEvent();

  void processCompleteFrame();
  void renderAndSendNextFrame();
  void processInitialFrame();
  void resetFrameStatistics();

  SampleRate sampleRate_;
  VideoStandard currentStandard_{UNKNOWN_VIDEO_STANDARD};
  std::unique_ptr<FrameCanvas> frameCanvas_{nullptr};

  bool isStandardDetected_{false};
  bool isInterlaced_{false};
  bool isTFF_{true};
  bool isSyncLocked_{false};
  bool isSyncingWithTopField_{false};

  struct FrameInfo {
    uint32_t initialFrameNumber;
    bool hasTopFieldPart;
    bool hasBottomFieldPart;
  };
  std::optional<FrameInfo> currentProcessingFrame_{std::nullopt};
  uint32_t frameCounter_{0};

  uint32_t lineLeftPadding_{0};
  uint32_t lineRightPadding_{0};
  uint32_t lineHorizontalPadding_{0};

  uint32_t equalizationBlackLevelCalcRegionSamples_{0};
  uint32_t equalizationBlackLevelCalcRegionOffset_{0};
  VSampleFloat equalizationBlackLevel_{0.0f};

  WhiteLevelCalibrator whiteLevelCalibrator_;
  float nominalFrameRate_{0.0f};

  FrameCompositionResult currentCompositionResult_;
};

} // namespace IQVideoProcessor::Pipeline
```

### frame_composition_node.cpp
```cpp
#include "./frame_composition_node.h"
#include "logging/logging.h"
#include <cmath>
#include <iostream>
#include <sstream>
#include "../../video_processor_configs.h"

namespace IQVideoProcessor::Pipeline {

namespace {
constexpr float FALLBACK_FRAME_RATE = 25.0f;
} // namespace

FrameCompositionNode::FrameCompositionNode(const SampleRate sampleRate)
  : sampleRate_(sampleRate),
    whiteLevelCalibrator_(getWhiteLevelCalibratorDefaults()) {
  setRunning();
}

FrameCompositionNode::~FrameCompositionNode() {
  PipelineComponent::stop();
}

bool FrameCompositionNode::process(LineDetectionEvent& event) {
  if (!running()) return false;

  switch (event.type) {
    case LineDetectionEventType::STANDARD_DETECTED:
      handleStandardDetected(event.videoStandard);
      break;
    case LineDetectionEventType::SYNC_LOCKED:
      handleSyncLock();
      break;
    case LineDetectionEventType::SYNC_LOCK_LOST:
      handleSyncLockLost();
      break;
    case LineDetectionEventType::FRAME_FIELD_BEGIN:
      handleFrameFieldBegin(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::FRAME_FIELD_END:
      handleFrameFieldEnd(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::LINE_RECEIVED:
      handleLineReceived(event.data, event.dataSize, event.lineNumber, event.isTopField);
      break;
    case LineDetectionEventType::EQUALIZATION:
      handleEqualization(event.data, event.dataSize);
      break;
    case LineDetectionEventType::UNKNOWN:
    default:
      handleUnknownEvent();
      break;
  }

  return running();
}

void FrameCompositionNode::handleStandardDetected(const VideoStandard standard) {
  if (standard == UNKNOWN_VIDEO_STANDARD) {
    isStandardDetected_ = false;
    currentStandard_ = UNKNOWN_VIDEO_STANDARD;
    frameCanvas_.reset();
    whiteLevelCalibrator_.reset();
    currentCompositionResult_.data.clear();
    currentCompositionResult_.dataSize = 0;
    LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown video standard received");
    return;
  }

  if (currentStandard_ == standard && frameCanvas_) {
    return;
  }

  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "switching video standard from " << currentStandard_ << " to " << standard);

  auto [width, height] = getVideoStandardDimensions(standard);
  frameCanvas_ = std::make_unique<FrameCanvas>(width, height);
  currentStandard_ = standard;
  isInterlaced_ = isVideoStandardInterlaced(standard);
  isTFF_ = isVideoStandardTFF(standard);

  auto [leftPaddingSec, rightPaddingSec] = getVideoStandardPaddings(standard);
  lineLeftPadding_ = static_cast<uint32_t>(std::ceil(leftPaddingSec * sampleRate_));
  lineRightPadding_ = static_cast<uint32_t>(std::ceil(rightPaddingSec * sampleRate_));
  lineHorizontalPadding_ = lineLeftPadding_ + lineRightPadding_;

  currentCompositionResult_.data.resize(frameCanvas_->getMinRenderBufferSize());
  currentCompositionResult_.width = width;
  currentCompositionResult_.height = height;
  currentCompositionResult_.videoStandard = standard;
  currentCompositionResult_.frameNumber = 0;
  currentCompositionResult_.dataSize = 0;

  equalizationBlackLevelCalcRegionSamples_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_REGION_SEC * sampleRate_));
  equalizationBlackLevelCalcRegionOffset_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_OFFSET_SEC * sampleRate_));
  equalizationBlackLevel_ = 0.0f;

  nominalFrameRate_ = getVideoStandardFrameRate(standard);
  if (nominalFrameRate_ <= 0.0f) {
    nominalFrameRate_ = FALLBACK_FRAME_RATE;
  }

  whiteLevelCalibrator_.reset();
  whiteLevelCalibrator_.setExpectedLineCount(static_cast<std::size_t>(height));
  whiteLevelCalibrator_.setNominalFrameRate(nominalFrameRate_);
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);

  isStandardDetected_ = true;
  processInitialFrame();
}

void FrameCompositionNode::handleSyncLock() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock acquired");
  isSyncLocked_ = true;
}

void FrameCompositionNode::handleSyncLockLost() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock lost");
  isSyncLocked_ = false;
  processCompleteFrame();
}

void FrameCompositionNode::handleFrameFieldBegin(const uint32_t frameFieldNumber, const bool isTopField) {
  if (!isStandardDetected_ || !isSyncLocked_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "warning - frame field begin received but standard not detected or sync not locked");
    return;
  }

  if (!currentProcessingFrame_.has_value()) {
    if (!isTopField) {
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "waiting for top field to start frame processing...");
      isSyncingWithTopField_ = true;
      return;
    }
    isSyncingWithTopField_ = false;
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
    resetFrameStatistics();
    return;
  }

  if (isTopField || !isInterlaced_) {
    isSyncingWithTopField_ = false;
    processCompleteFrame();
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
    resetFrameStatistics();
    LOG_VERBOSE(FRAME_COMPOSITION_NODE, "begin processing next top field in series!");
  } else {
    auto& cfi = currentProcessingFrame_.value();
    cfi.hasBottomFieldPart = true;
  }
}

void FrameCompositionNode::handleFrameFieldEnd(const uint32_t /*frameFieldNumber*/, const bool /*isTopField*/) {
  if (!currentProcessingFrame_.has_value()) return;

  const auto& cfi = currentProcessingFrame_.value();
  if ((cfi.hasTopFieldPart && cfi.hasBottomFieldPart) || (!isInterlaced_ && cfi.hasTopFieldPart)) {
    processCompleteFrame();
  }
}

void FrameCompositionNode::handleLineReceived(
  const std::vector<VSampleFloat>& data,
  const uint32_t dataSize,
  const uint32_t lineNumber,
  const bool isTopField
) {
  if (!isStandardDetected_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but standard is not detected");
    return;
  }
  if (!currentProcessingFrame_.has_value()) {
    if (!isSyncingWithTopField_) {
      LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but no frame field is active");
    }
    return;
  }

  const uint32_t croppedDataStart = lineLeftPadding_;
  const uint32_t croppedDataSize = dataSize > lineHorizontalPadding_ ? dataSize - lineHorizontalPadding_ : 0U;
  if (croppedDataSize == 0U) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but padding exceeds data size");
    return;
  }

  const VSampleFloat* croppedData = &data[croppedDataStart];
  if (isInterlaced_) {
    const uint32_t lineIndex = (lineNumber << 1) + (isTFF_ == isTopField ? 1U : 0U);
    frameCanvas_->setVideoLineRawData(lineIndex, croppedData, croppedDataSize);
  } else {
    frameCanvas_->setVideoLineRawData(lineNumber, croppedData, croppedDataSize);
  }

  whiteLevelCalibrator_.accumulateLinePeak(croppedData, croppedDataSize);
}

void FrameCompositionNode::handleEqualization(const std::vector<VSampleFloat>& data, const uint32_t dataSize) {
  if (equalizationBlackLevelCalcRegionOffset_ + equalizationBlackLevelCalcRegionSamples_ > dataSize) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "equalization data received but not enough samples to calculate black level");
    equalizationBlackLevel_ = data[dataSize >> 1];
    return;
  }

  float accumulator = 0.0f;
  const VSampleFloat* blackRegionData = &data[equalizationBlackLevelCalcRegionOffset_];
  for (uint32_t i = 0; i < equalizationBlackLevelCalcRegionSamples_; ++i) {
    accumulator += blackRegionData[i];
  }
  equalizationBlackLevel_ = static_cast<VSampleFloat>(
    accumulator / static_cast<float>(equalizationBlackLevelCalcRegionSamples_)
  );
}

void FrameCompositionNode::handleUnknownEvent() {
  LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown event received");
}

void FrameCompositionNode::processCompleteFrame() {
  if (!currentProcessingFrame_.has_value()) return;

  whiteLevelCalibrator_.finalizeFrame();
  renderAndSendNextFrame();
  currentProcessingFrame_.reset();
}

void FrameCompositionNode::renderAndSendNextFrame() {
  if (!frameCanvas_) return;

  const VSampleFloat whiteLevel = whiteLevelCalibrator_.currentWhiteLevel();
  const auto renderResult = frameCanvas_->render(
    currentCompositionResult_.data.data(),
    currentCompositionResult_.data.size(),
    equalizationBlackLevel_,
    whiteLevel
  );

  if (renderResult.has_value()) {
    currentCompositionResult_.dataSize = renderResult.value();
    currentCompositionResult_.frameNumber = frameCounter_++;

    if (!sendOutput(currentCompositionResult_)) {
      stop();
    }
  } else {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "failed to render the frame");
    stop();
  }
}

void FrameCompositionNode::processInitialFrame() {
  if (frameCanvas_) {
    frameCanvas_->reset();
  }
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);
  whiteLevelCalibrator_.finalizeFrame();
  renderAndSendNextFrame();
}

void FrameCompositionNode::resetFrameStatistics() {
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);
}

} // namespace IQVideoProcessor::Pipeline
```

### Patch for `video_processor_configs`
```diff
diff --git a/video_processor_configs.h b/video_processor_configs.h
index 0000000..1111111 100644
--- a/video_processor_configs.h
+++ b/video_processor_configs.h
@@ -1,6 +1,11 @@
 #pragma once
 #include "../../../types.h"
 
 namespace IQVideoProcessor::Pipeline {
+
+struct WhiteLevelCalibratorSettings;
+
+const WhiteLevelCalibratorSettings& getWhiteLevelCalibratorDefaults();
+float getVideoStandardFrameRate(VideoStandard standard);
+
 } // namespace IQVideoProcessor::Pipeline
diff --git a/video_processor_configs.cpp b/video_processor_configs.cpp
index 0000000..2222222 100644
--- a/video_processor_configs.cpp
+++ b/video_processor_configs.cpp
@@ -1,5 +1,7 @@
 #include "./video_processor_configs.h"
+#include "./frame_composition_node/partials/white_level_calibrator.h"
 #include <cmath>
 
 namespace IQVideoProcessor::Pipeline {
 
+namespace {
+constexpr float DEFAULT_ATTACK_PERCENT_PER_SECOND = 120.0f;
+constexpr float DEFAULT_RELEASE_PERCENT_PER_SECOND = 45.0f;
+constexpr float DEFAULT_TOP_FRACTION = 0.12f;
+constexpr std::size_t DEFAULT_MIN_TOP_SAMPLE_COUNT = 12;
+constexpr VSampleFloat DEFAULT_MIN_DYNAMIC_RANGE = 0.12f;
+constexpr VSampleFloat DEFAULT_INITIAL_WHITE_LEVEL = static_cast<VSampleFloat>(M_PI);
+} // namespace
+
+const WhiteLevelCalibratorSettings& getWhiteLevelCalibratorDefaults() {
+  static const WhiteLevelCalibratorSettings SETTINGS{
+    DEFAULT_TOP_FRACTION,
+    DEFAULT_MIN_TOP_SAMPLE_COUNT,
+    DEFAULT_MIN_DYNAMIC_RANGE,
+    DEFAULT_INITIAL_WHITE_LEVEL,
+    DEFAULT_ATTACK_PERCENT_PER_SECOND,
+    DEFAULT_RELEASE_PERCENT_PER_SECOND
+  };
+  return SETTINGS;
+}
+
+float getVideoStandardFrameRate(VideoStandard /*standard*/) {
+  // TODO: provide the actual mapping for each supported video standard.
+  return 0.0f;
+}
+
 } // namespace IQVideoProcessor::Pipeline
```

Please apply the patch above to `video_processor_configs.h/.cpp`, then integrate the new module files. Once the frame-rate mapping is populated, `FrameCompositionNode` will automatically inherit the correct behaviour.