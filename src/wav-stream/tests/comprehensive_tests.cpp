#include "../wav_stream.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <cstring>
#include <cassert>
#include <chrono>
#include <memory>

// Advanced test utilities
class AdvancedWAVTestUtils {
public:
    // Create a large test WAV file for performance testing
    static bool createLargeTestWAV(const std::string& filename, 
                                  uint32_t sampleRate = 44100,
                                  uint32_t numSamples = 100000) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        uint16_t bitsPerSample = 16;
        uint16_t numChannels = 2;
        uint32_t dataSize = numSamples * numChannels * (bitsPerSample / 8);
        uint32_t fileSize = 36 + dataSize;
        
        // Write WAV header
        file.write("RIFF", 4);
        file.write(reinterpret_cast<const char*>(&fileSize), 4);
        file.write("WAVE", 4);
        file.write("fmt ", 4);
        
        uint32_t fmtSize = 16;
        file.write(reinterpret_cast<const char*>(&fmtSize), 4);
        
        uint16_t audioFormat = 1;
        file.write(reinterpret_cast<const char*>(&audioFormat), 2);
        file.write(reinterpret_cast<const char*>(&numChannels), 2);
        file.write(reinterpret_cast<const char*>(&sampleRate), 4);
        
        uint32_t byteRate = sampleRate * numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&byteRate), 4);
        
        uint16_t blockAlign = numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&blockAlign), 2);
        file.write(reinterpret_cast<const char*>(&bitsPerSample), 2);
        
        file.write("data", 4);
        file.write(reinterpret_cast<const char*>(&dataSize), 4);
        
        // Write sample data with pattern
        for (uint32_t i = 0; i < numSamples; ++i) {
            int16_t i_sample = static_cast<int16_t>(i % 32767);
            int16_t q_sample = static_cast<int16_t>((i * 3) % 32767);
            file.write(reinterpret_cast<const char*>(&i_sample), 2);
            file.write(reinterpret_cast<const char*>(&q_sample), 2);
        }
        
        file.close();
        return true;
    }
    
    // Create WAV with specific pattern for validation
    static bool createPatternWAV(const std::string& filename, 
                                uint32_t numSamples = 1000) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        uint32_t sampleRate = 44100;
        uint16_t bitsPerSample = 16;
        uint16_t numChannels = 2;
        uint32_t dataSize = numSamples * numChannels * (bitsPerSample / 8);
        uint32_t fileSize = 36 + dataSize;
        
        // Write WAV header
        file.write("RIFF", 4);
        file.write(reinterpret_cast<const char*>(&fileSize), 4);
        file.write("WAVE", 4);
        file.write("fmt ", 4);
        
        uint32_t fmtSize = 16;
        file.write(reinterpret_cast<const char*>(&fmtSize), 4);
        
        uint16_t audioFormat = 1;
        file.write(reinterpret_cast<const char*>(&audioFormat), 2);
        file.write(reinterpret_cast<const char*>(&numChannels), 2);
        file.write(reinterpret_cast<const char*>(&sampleRate), 4);
        
        uint32_t byteRate = sampleRate * numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&byteRate), 4);
        
        uint16_t blockAlign = numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&blockAlign), 2);
        file.write(reinterpret_cast<const char*>(&bitsPerSample), 2);
        
        file.write("data", 4);
        file.write(reinterpret_cast<const char*>(&dataSize), 4);
        
        // Write predictable pattern: I = sample_index, Q = sample_index * 2
        for (uint32_t i = 0; i < numSamples; ++i) {
            int16_t i_sample = static_cast<int16_t>(i % 1000);
            int16_t q_sample = static_cast<int16_t>((i * 2) % 1000);
            file.write(reinterpret_cast<const char*>(&i_sample), 2);
            file.write(reinterpret_cast<const char*>(&q_sample), 2);
        }
        
        file.close();
        return true;
    }
    
    // Cleanup test files
    static void cleanup(const std::vector<std::string>& filenames) {
        for (const auto& filename : filenames) {
            std::remove(filename.c_str());
        }
    }
};

// Test sample packing correctness
int test_sample_packing() {
    std::cout << "\n--- Testing Sample Packing ---" << std::endl;
    
    const std::string testFile = "test_packing.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create WAV with known pattern
        if (!AdvancedWAVTestUtils::createPatternWAV(testFile, 100)) {
            std::cout << "❌ Failed to create pattern WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile);
        if (!stream.open()) {
            std::cout << "❌ Failed to open pattern WAV: " << stream.lastError() << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Read samples and verify packing
        std::vector<RawIQSample> samples(10);
        if (!stream.readSamples(samples.data(), 10)) {
            std::cout << "❌ Failed to read samples" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Verify first few samples match expected pattern
        for (int i = 0; i < 5; ++i) {
            RawIQSample sample = samples[i];
            uint16_t i_part = static_cast<uint16_t>(sample & 0xFFFF);
            uint16_t q_part = static_cast<uint16_t>((sample >> 16) & 0xFFFF);
            
            uint16_t expected_i = static_cast<uint16_t>(i % 1000);
            uint16_t expected_q = static_cast<uint16_t>((i * 2) % 1000);
            
            if (i_part != expected_i || q_part != expected_q) {
                std::cout << "❌ Sample " << i << " packing incorrect: "
                         << "got I=" << i_part << " Q=" << q_part
                         << ", expected I=" << expected_i << " Q=" << expected_q << std::endl;
                AdvancedWAVTestUtils::cleanup(testFiles);
                return 1;
            }
        }
        
        std::cout << "✓ Sample packing is correct (0xQQQQIIII format)" << std::endl;
        
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in sample packing test: " << e.what() << std::endl;
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test end-of-stream behavior
int test_end_of_stream() {
    std::cout << "\n--- Testing End-of-Stream Behavior ---" << std::endl;
    
    const std::string testFile = "test_eos.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create small WAV file with only 10 samples
        if (!AdvancedWAVTestUtils::createPatternWAV(testFile, 10)) {
            std::cout << "❌ Failed to create small WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile);
        if (!stream.open()) {
            std::cout << "❌ Failed to open small WAV: " << stream.lastError() << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Read all samples
        std::vector<RawIQSample> samples(10);
        if (!stream.readSamples(samples.data(), 10)) {
            std::cout << "❌ Failed to read all samples" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read all 10 samples" << std::endl;
        
        // Try to read more samples (should fail)
        std::vector<RawIQSample> extraSamples(5);
        if (stream.readSamples(extraSamples.data(), 5)) {
            std::cout << "❌ Should not be able to read past end of stream" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Correctly failed to read past end of stream" << std::endl;
        
        // Check that stream is no longer active
        if (stream.isActive()) {
            std::cout << "❌ Stream should not be active after EOS" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Stream correctly marked as inactive after EOS" << std::endl;
        
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in end-of-stream test: " << e.what() << std::endl;
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test multiple open/close cycles
int test_multiple_cycles() {
    std::cout << "\n--- Testing Multiple Open/Close Cycles ---" << std::endl;
    
    const std::string testFile = "test_cycles.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!AdvancedWAVTestUtils::createPatternWAV(testFile, 100)) {
            std::cout << "❌ Failed to create test WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile);
        
        // Test multiple open/close cycles
        for (int cycle = 0; cycle < 5; ++cycle) {
            // Open
            if (!stream.open()) {
                std::cout << "❌ Failed to open on cycle " << cycle << ": " << stream.lastError() << std::endl;
                AdvancedWAVTestUtils::cleanup(testFiles);
                return 1;
            }
            
            // Read some samples
            std::vector<RawIQSample> samples(10);
            if (!stream.readSamples(samples.data(), 10)) {
                std::cout << "❌ Failed to read samples on cycle " << cycle << std::endl;
                AdvancedWAVTestUtils::cleanup(testFiles);
                return 1;
            }
            
            // Close
            stream.close();
            if (stream.isActive()) {
                std::cout << "❌ Stream should not be active after close on cycle " << cycle << std::endl;
                AdvancedWAVTestUtils::cleanup(testFiles);
                return 1;
            }
        }
        
        std::cout << "✓ Successfully completed 5 open/close cycles" << std::endl;
        
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in multiple cycles test: " << e.what() << std::endl;
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test interface compliance
int test_interface_compliance() {
    std::cout << "\n--- Testing IIQStream Interface Compliance ---" << std::endl;
    
    const std::string testFile = "test_interface.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!AdvancedWAVTestUtils::createPatternWAV(testFile, 50)) {
            std::cout << "❌ Failed to create test WAV file" << std::endl;
            return 1;
        }
        
        // Test through interface pointer
        std::unique_ptr<IIQStream> stream = std::make_unique<WAVIQStream>(testFile);
        
        // Test interface methods
        if (stream->sourceName() != "wav") {
            std::cout << "❌ Interface sourceName() failed" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Interface sourceName() works" << std::endl;
        
        // Open through interface
        WAVIQStream* wavStream = static_cast<WAVIQStream*>(stream.get());
        if (!wavStream->open()) {
            std::cout << "❌ Failed to open through interface" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        if (stream->sampleRate() == 0) {
            std::cout << "❌ Interface sampleRate() failed" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Interface sampleRate() works: " << stream->sampleRate() << std::endl;
        
        if (!stream->isActive()) {
            std::cout << "❌ Interface isActive() failed" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Interface isActive() works" << std::endl;
        
        // Read through interface
        std::vector<RawIQSample> samples(10);
        if (!stream->readSamples(samples.data(), 10)) {
            std::cout << "❌ Interface readSamples() failed" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Interface readSamples() works" << std::endl;
        
        // Close through interface
        stream->close();
        if (stream->isActive()) {
            std::cout << "❌ Interface close() failed" << std::endl;
            AdvancedWAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Interface close() works" << std::endl;
        
        if (!stream->lastError().empty()) {
            std::cout << "✓ Interface lastError() works: " << stream->lastError() << std::endl;
        } else {
            std::cout << "✓ Interface lastError() works (no error)" << std::endl;
        }
        
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in interface compliance test: " << e.what() << std::endl;
        AdvancedWAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Main comprehensive test function
int run_comprehensive_tests() {
    std::cout << "Running comprehensive WAVIQStream tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_sample_packing();
    failures += test_end_of_stream();
    failures += test_multiple_cycles();
    failures += test_interface_compliance();
    
    if (failures == 0) {
        std::cout << "\n🎉 All comprehensive WAVIQStream tests passed!" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " comprehensive test(s) failed!" << std::endl;
    }
    
    return failures;
}
