#include "wav_stream.h"
#include <cstring>
#include <algorithm>
#include <thread>
#include <chrono>
#include <utility>
#include <vector>

WAVIQStream::WAVIQStream(std::string filename, const bool enableLoop, const bool enableTiming)
    : filename_(std::move(filename))
    , sourceName_("wav")
    , sampleRate_(0)
    , bitsPerSample_(0)
    , numChannels_(0)
    , dataStartPos_(0)
    , dataSize_(0)
    , currentPos_(0)
    , isActive_(false)
    , isOpen_(false)
    , loopEnabled_(enableLoop)
    , totalSamplesRead_(0)
    , timingEnabled_(enableTiming)
    , nanosPerSample_(0.0) {
}

WAVIQStream::~WAVIQStream() {
    WAVIQStream::close();
}

bool WAVIQStream::open() {
    if (isOpen_) {
        close();
    }

    file_.open(filename_, std::ios::binary);
    if (!file_.is_open()) {
        return setError("Failed to open file: " + filename_);
    }

    if (!parseHeader()) {
        close();
        return false;
    }

    isOpen_ = true;
    isActive_ = true;
    currentPos_ = 0;
    totalSamplesRead_ = 0;
    lastError_.clear();

    return true;
}

bool WAVIQStream::parseHeader() {
    WAVHeader header{};
    
    // Read the header
    file_.read(reinterpret_cast<char*>(&header), sizeof(WAVHeader));
    if (file_.gcount() != sizeof(WAVHeader)) {
        return setError("Failed to read WAV header");
    }

    // Validate RIFF signature
    if (std::memcmp(header.riff, "RIFF", 4) != 0) {
        return setError("Invalid RIFF signature");
    }

    // Validate WAVE signature
    if (std::memcmp(header.wave, "WAVE", 4) != 0) {
        return setError("Invalid WAVE signature");
    }

    // Validate format chunk
    if (std::memcmp(header.fmt, "fmt ", 4) != 0) {
        return setError("Invalid format chunk");
    }

    // Validate PCM format
    if (header.audioFormat != 1) {
        return setError("Only PCM format is supported");
    }

    // Validate channel count (must be 2 for I/Q)
    if (header.numChannels != 2) {
        return setError("WAV file must have exactly 2 channels (I and Q)");
    }

    // Validate bits per sample
    if (header.bitsPerSample != 16 && header.bitsPerSample != 32) {
        return setError("Only 16-bit and 32-bit samples are supported");
    }

    // Validate data chunk
    if (std::memcmp(header.data, "data", 4) != 0) {
        return setError("Invalid data chunk");
    }

    // Store parsed values
    sampleRate_ = header.sampleRate;
    bitsPerSample_ = header.bitsPerSample;
    numChannels_ = header.numChannels;
    dataSize_ = header.dataSize;
    dataStartPos_ = file_.tellg();

    // Calculate timing parameters for simulation
    if (timingEnabled_ && sampleRate_ > 0) {
        nanosPerSample_ = 1e9 / static_cast<double>(sampleRate_);
    } else {
        nanosPerSample_ = 0.0;
    }

    // Validate data size alignment
    uint32_t bytesPerSample = (bitsPerSample_ / 8) * numChannels_;
    if (dataSize_ % bytesPerSample != 0) {
        return setError("Data size is not aligned to sample boundary");
    }

    return true;
}

bool WAVIQStream::readSamples(RawIQSample* dst, uint32_t sampleCount) {
    if (!isActive_ || !isOpen_) {
        return false;
    }

    if (sampleCount == 0) {
        return true;
    }

    uint32_t bytesPerSample = (bitsPerSample_ / 8) * numChannels_;
    uint32_t totalSamples = dataSize_ / bytesPerSample;
    uint32_t samplesRead = 0;

    while (samplesRead < sampleCount) {
        // Check if we've reached end of stream
        if (currentPos_ >= totalSamples) {
            if (loopEnabled_) {
                // Reset to beginning for looping
                if (!reset()) {
                    return false;
                }
            } else {
                // No looping, mark as inactive and return
                isActive_ = false;
                return samplesRead > 0; // Return true if we read some samples
            }
        }

        // Calculate how many samples we can read in this iteration
        uint32_t remainingSamples = sampleCount - samplesRead;
        uint32_t availableSamples = totalSamples - currentPos_;
        const uint32_t samplesToRead = std::min(remainingSamples, availableSamples);
        const uint32_t bytesToRead = samplesToRead * bytesPerSample;

        if (bitsPerSample_ == 16) {
            // Read 16-bit samples
            std::vector<int16_t> buffer(samplesToRead * 2); // I and Q
            file_.read(reinterpret_cast<char*>(buffer.data()), bytesToRead);

            if (file_.gcount() != static_cast<std::streamsize>(bytesToRead)) {
                isActive_ = false;
                return setError("Failed to read expected number of bytes");
            }

            // Convert to SampleType format
            for (uint32_t i = 0; i < samplesToRead; ++i) {
                const int16_t iSample = buffer[i * 2];     // I component
                const int16_t qSample = buffer[i * 2 + 1]; // Q component
                dst[samplesRead + i] = packSample16(iSample, qSample);
            }
        } else { // 32-bit
            // Read 32-bit samples
            std::vector<int32_t> buffer(samplesToRead * 2); // I and Q
            file_.read(reinterpret_cast<char*>(buffer.data()), bytesToRead);

            if (file_.gcount() != static_cast<std::streamsize>(bytesToRead)) {
                isActive_ = false;
                return setError("Failed to read expected number of bytes");
            }

            // Convert to SampleType format
            for (uint32_t i = 0; i < samplesToRead; ++i) {
                const int32_t iSample = buffer[i * 2];     // I component
                const int32_t qSample = buffer[i * 2 + 1]; // Q component
                dst[samplesRead + i] = packSample32(iSample, qSample);
            }
        }

        currentPos_ += samplesToRead;
        samplesRead += samplesToRead;
        totalSamplesRead_ += samplesToRead;
    }

    // Apply timing simulation if enabled
    if (timingEnabled_ && nanosPerSample_ > 0.0) {
        // Calculate expected duration for the samples read
        double expectedDurationNs = static_cast<double>(sampleCount) * nanosPerSample_;

        // Only apply timing simulation for durations longer than 100 microseconds
        // to avoid overhead and inaccuracy of very short sleeps
        if (expectedDurationNs >= 100000.0) { // 100 microseconds
            const auto expectedDuration = std::chrono::nanoseconds(static_cast<long long>(expectedDurationNs));
            // Use high-resolution sleep for better accuracy
            std::this_thread::sleep_for(expectedDuration);
        }
    }

    return true;
}

SampleRate WAVIQStream::sampleRate() const noexcept {
    return sampleRate_;
}

const std::string& WAVIQStream::sourceName() const noexcept {
    return sourceName_;
}

bool WAVIQStream::isActive() const noexcept {
    return isActive_ && isOpen_;
}

void WAVIQStream::close() noexcept {
    if (file_.is_open()) {
        file_.close();
    }
    isOpen_ = false;
    isActive_ = false;
}

const std::string& WAVIQStream::lastError() const noexcept {
    return lastError_;
}

bool WAVIQStream::setError(const std::string& error) {
    lastError_ = error;
    isActive_ = false;
    return false;
}

void WAVIQStream::setLooping(bool enable) noexcept {
    loopEnabled_ = enable;
}

bool WAVIQStream::isLooping() const noexcept {
    return loopEnabled_;
}

uint64_t WAVIQStream::getTotalSamplesRead() const noexcept {
    return totalSamplesRead_;
}

bool WAVIQStream::reset() {
    if (!isOpen_) {
        return setError("Cannot reset: stream is not open");
    }

    // Seek back to start of data
    file_.seekg(dataStartPos_, std::ios::beg);
    if (file_.fail()) {
        return setError("Failed to seek to start of data");
    }

    currentPos_ = 0;
    isActive_ = true;
    lastError_.clear();

    return true;
}

void WAVIQStream::setTimingEnabled(bool enable) noexcept {
    timingEnabled_ = enable;

    // Recalculate timing parameters if enabling and we have a valid sample rate
    if (enable && sampleRate_ > 0) {
        nanosPerSample_ = 1e9 / static_cast<double>(sampleRate_);
    } else {
        nanosPerSample_ = 0.0;
    }
}

bool WAVIQStream::isTimingEnabled() const noexcept {
    return timingEnabled_;
}