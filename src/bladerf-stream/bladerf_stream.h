#pragma once
#include "../iiq-stream/iiq_stream.h"
#include <libbladeRF.h>
#include <string>
#include <atomic>

/**
 * BladeRFIQStream (synchronous pull)
 */
class BladeRFIQStream final : public IIQStream {
public:
  enum class GainMode {
    MANUAL,
    FAST,
    SLOW,
    DEFAULT_
  };

  struct Config {
    double frequencyHz = 915e6;
    double sampleRateHz = 1.0e6;
    double bandwidthHz = 1.5e6;
    int channel = 0;      // RX(0) or RX(1) if available
    GainMode gainMode = GainMode::MANUAL;
    int manualGainDb = 30;    // Used only when gainMode == MANUAL
  };

  struct ResolvedInfo {
    std::string sourceName = "bladeRF";
    double frequencyHz = 0.0;
    double sampleRateHz = 0.0;
    double bandwidthHz = 0.0;
    int channel = 0;
    GainMode gainMode = GainMode::MANUAL;
    int manualGainDb = 0;     // Valid when mode is MANUAL
  };

  struct BufferConfig {
    uint32_t numBuffers{};
    uint32_t bufferSize{};
    uint32_t numTransfers{};
  };

  explicit BladeRFIQStream(const Config& cfg, const BufferConfig& bufferCfg);
  ~BladeRFIQStream() override;

  // Lifecycle
  bool open();
  void close() noexcept override;

  // IIQStream
  bool readSamples(RawIQSample* dst, uint32_t sampleCount) override;
  SampleRate sampleRate() const noexcept override;
  const std::string& sourceName() const noexcept override;
  bool isActive() const noexcept override;
  const std::string& lastError() const noexcept override;

  // Access resolved (actual) parameters after open()
  ResolvedInfo getResolvedInfo() const noexcept;

private:
  // Internal helpers
  bool setError(const std::string& err);
  bool configureDevice();
  bool configureSyncRx();

  // Device
  bladerf * dev_ = nullptr;
  std::string lastError_;
  std::string sourceName_ = "bladeRF";

  // State
  std::atomic<bool> isOpen_{false};
  std::atomic<bool> active_{false};

  // Config and resolved info
  Config cfg_;
  BufferConfig bufferCfg_;
  ResolvedInfo info_;

  static bladerf_gain_mode mapGainMode(GainMode m);
};
