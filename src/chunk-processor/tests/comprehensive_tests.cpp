#include "../chunk_processor.h"
#include <iostream>
#include <vector>
#include <random>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <cmath>

// Test configuration structure
struct TestConfig {
    uint32_t writeChunkSize;
    uint32_t readChunkSize;
    uint32_t readOverlapSize;
    uint32_t numOfWriteChunks;
    uint32_t numWrites;
    std::string description;
    
    TestConfig(uint32_t wcs, uint32_t rcs, uint32_t ros, uint32_t nwc, uint32_t nw, const std::string& desc)
        : writeChunkSize(wcs), readChunkSize(rcs), readOverlapSize(ros), 
          numOfWriteChunks(nwc), numWrites(nw), description(desc) {}
};

// Enhanced chunk collector with validation (template-based)
template<typename SampleType = uint32_t>
class ComprehensiveChunkCollector {
public:
    std::vector<std::vector<SampleType>> chunks;
    uint32_t expected_chunk_size;
    uint32_t expected_overlap;
    bool validation_enabled = true;

    ComprehensiveChunkCollector(uint32_t chunk_size, uint32_t overlap)
        : expected_chunk_size(chunk_size), expected_overlap(overlap) {}

    void operator()(const SampleType* data, uint32_t length) {
        if (validation_enabled && length != expected_chunk_size) {
            std::cerr << "❌ Chunk size mismatch: expected " << expected_chunk_size
                      << ", got " << length << std::endl;
            exit(1);
        }

        chunks.emplace_back(data, data + length);
    }
    
    void clear() {
        chunks.clear();
    }
    
    // Verify overlap between consecutive chunks
    bool verify_overlaps() const {
        if (chunks.size() < 2) return true;
        
        uint32_t stride = expected_chunk_size - expected_overlap;
        
        for (uint32_t i = 1; i < chunks.size(); ++i) {
            const auto& prev_chunk = chunks[i-1];
            const auto& curr_chunk = chunks[i];
            
            // Compare overlap region: last 'overlap' elements of prev with first 'overlap' elements of curr
            for (uint32_t j = 0; j < expected_overlap; ++j) {
                if (prev_chunk[stride + j] != curr_chunk[j]) {
                    std::cerr << "❌ Overlap mismatch at chunk " << i << ", position " << j 
                              << ": " << prev_chunk[stride + j] << " != " << curr_chunk[j] << std::endl;
                    return false;
                }
            }
        }
        return true;
    }
    
    // Verify data continuity across chunks
    bool verify_data_continuity() const {
        if (chunks.empty()) return true;
        
        uint32_t stride = expected_chunk_size - expected_overlap;
        
        for (uint32_t i = 1; i < chunks.size(); ++i) {
            const auto& prev_chunk = chunks[i-1];
            const auto& curr_chunk = chunks[i];
            
            // Check that data progresses correctly
            SampleType expected_start = prev_chunk[0] + stride;
            if (curr_chunk[0] != expected_start) {
                std::cerr << "❌ Data continuity broken at chunk " << i
                          << ": expected start " << expected_start
                          << ", got " << curr_chunk[0] << std::endl;
                return false;
            }
        }
        return true;
    }
};

// Test execution function
bool run_test_configuration(const TestConfig& config, int test_number) {
    std::cout << "\n[" << std::setw(2) << test_number << "] " << config.description << std::endl;
    std::cout << "    WC=" << config.writeChunkSize 
              << ", RC=" << config.readChunkSize 
              << ", OV=" << config.readOverlapSize 
              << ", NWC=" << config.numOfWriteChunks 
              << ", Writes=" << config.numWrites << std::endl;
    
    try {
        ComprehensiveChunkCollector<> collector(config.readChunkSize, config.readOverlapSize);
        auto handler = [&collector](const uint32_t* data, uint32_t chunkSize, uint32_t overlapSize) {
            collector(data, chunkSize);
        };

        ChunkProcessor<> processor(config.writeChunkSize, config.readChunkSize,
                                 config.readOverlapSize, config.numOfWriteChunks, handler);

        // Perform writes with sequential data
        uint32_t data_counter = 0;
        for (uint32_t write_idx = 0; write_idx < config.numWrites; ++write_idx) {
            uint32_t* writePtr = processor.getWriteChunkPtr();
            for (uint32_t i = 0; i < config.writeChunkSize; ++i) {
                writePtr[i] = data_counter++;
            }
            processor.commitWriteChunk();
        }
        
        // Validate results
        bool overlap_valid = collector.verify_overlaps();
        bool continuity_valid = collector.verify_data_continuity();
        
        if (overlap_valid && continuity_valid) {
            std::cout << "    ✓ PASS - " << collector.chunks.size() << " chunks generated" << std::endl;
            return true;
        } else {
            std::cout << "    ❌ FAIL - Validation errors" << std::endl;
            return false;
        }
        
    } catch (const std::exception& e) {
        std::cout << "    ❌ FAIL - Exception: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "    ❌ FAIL - Unknown exception" << std::endl;
        return false;
    }
}

// Main comprehensive test runner
int run_comprehensive_tests() {
    std::cout << "\n🔬 Running ChunkProcessor Comprehensive Tests" << std::endl;
    std::cout << "=============================================" << std::endl;
    
    // Define 30 comprehensive test configurations
    std::vector<TestConfig> test_configs = {
        // Basic configurations
        TestConfig(1024, 200, 50, 4, 3, "Basic small chunks"),
        TestConfig(2048, 512, 128, 4, 3, "Medium chunks with 25% overlap"),
        TestConfig(4096, 1024, 256, 4, 3, "Large chunks with 25% overlap"),
        
        // Various chunk sizes
        TestConfig(64, 32, 8, 8, 10, "Tiny chunks - 64/32/8"),
        TestConfig(128, 64, 16, 8, 8, "Small chunks - 128/64/16"),
        TestConfig(256, 128, 32, 6, 6, "Small-medium chunks - 256/128/32"),
        TestConfig(512, 256, 64, 6, 5, "Medium chunks - 512/256/64"),
        TestConfig(1024, 512, 128, 4, 4, "Large chunks - 1024/512/128"),
        TestConfig(2048, 1024, 256, 4, 3, "Very large chunks - 2048/1024/256"),
        TestConfig(4096, 2048, 512, 3, 3, "Huge chunks - 4096/2048/512"),
        
        // Different overlap ratios
        TestConfig(1000, 500, 50, 4, 4, "10% overlap ratio"),
        TestConfig(1000, 500, 100, 4, 4, "20% overlap ratio"),
        TestConfig(1000, 500, 150, 4, 4, "30% overlap ratio"),
        TestConfig(1000, 500, 200, 4, 4, "40% overlap ratio"),
        TestConfig(1000, 500, 250, 4, 4, "50% overlap ratio"),
        
        // Various write batch sizes
        TestConfig(100, 200, 50, 8, 15, "Small writes, large reads"),
        TestConfig(500, 200, 50, 6, 8, "Medium writes, small reads"),
        TestConfig(1500, 500, 100, 4, 5, "Large writes, medium reads"),
        TestConfig(3000, 1000, 200, 3, 3, "Very large writes"),
        
        // Edge cases
        TestConfig(1000, 999, 1, 4, 3, "Minimal overlap (1 sample)"),
        TestConfig(1000, 500, 499, 4, 4, "Maximum overlap (499/500)"),
        TestConfig(100, 50, 25, 10, 20, "High frequency small chunks"),
        TestConfig(8192, 4096, 1024, 2, 2, "Maximum size chunks"),
        
        // Stress tests with unusual combinations
        TestConfig(73, 37, 11, 7, 13, "Prime number sizes"),
        TestConfig(1001, 503, 127, 5, 7, "Odd number combinations"),
        TestConfig(1024, 333, 111, 6, 9, "Non-power-of-2 reads"),
        TestConfig(777, 555, 222, 4, 6, "Random size combination"),
        TestConfig(2000, 800, 300, 3, 4, "Asymmetric overlap"),
        TestConfig(1500, 600, 150, 5, 8, "25% overlap with medium sizes"),
        TestConfig(3333, 1111, 333, 3, 3, "Triple-digit patterns"),
        TestConfig(4000, 2000, 800, 3, 3, "40% overlap large chunks")
    };
    
    int passed = 0;
    int total = test_configs.size();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < total; ++i) {
        if (run_test_configuration(test_configs[i], i + 1)) {
            passed++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "COMPREHENSIVE TEST RESULTS" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
    std::cout << "Passed: " << passed << "/" << total << " tests" << std::endl;
    std::cout << "Duration: " << duration.count() << "ms" << std::endl;
    
    if (passed == total) {
        std::cout << "🎉 ALL COMPREHENSIVE TESTS PASSED!" << std::endl;
        return 0;
    } else {
        std::cout << "❌ " << (total - passed) << " tests FAILED" << std::endl;
        return 1;
    }
}

// High-volume performance test - 20 million samples
int run_high_volume_performance_test() {
    std::cout << "\n🚀 Running High-Volume Performance Test" << std::endl;
    std::cout << "=======================================" << std::endl;
    std::cout << "Processing 20 million samples..." << std::endl;
    std::cout << "Write chunks: 8192 samples each" << std::endl;
    std::cout << "Read chunks: 4450 samples with 135 overlap" << std::endl;

    const uint32_t TOTAL_SAMPLES = 20000000;
    const uint32_t WRITE_CHUNK_SIZE = 8192;
    const uint32_t READ_CHUNK_SIZE = 4450;
    const uint32_t READ_OVERLAP = 135;
    const uint32_t READ_STRIDE = READ_CHUNK_SIZE - READ_OVERLAP;  // 4315

    // Calculate expected operations
    const uint32_t EXPECTED_WRITES = (TOTAL_SAMPLES + WRITE_CHUNK_SIZE - 1) / WRITE_CHUNK_SIZE;  // ~2442
    const uint32_t EXPECTED_READ_CHUNKS = (TOTAL_SAMPLES - READ_CHUNK_SIZE) / READ_STRIDE + 1;

    std::cout << "Expected write operations: " << EXPECTED_WRITES << std::endl;
    std::cout << "Expected read chunks: " << EXPECTED_READ_CHUNKS << std::endl;
    std::cout << "Read stride: " << READ_STRIDE << " samples" << std::endl;

    // Performance tracking
    uint32_t total_read_chunks = 0;
    uint32_t validation_chunks_checked = 0;
    uint32_t validation_samples_checked = 0;
    bool data_validation_passed = true;
    uint32_t glue_buffer_usage_estimate = 0;

    // Memory-efficient validation: check every Nth chunk and sample patterns
    const uint32_t VALIDATION_INTERVAL = 100;  // Check every 100th chunk
    const uint32_t SAMPLE_CHECK_INTERVAL = 50; // Check every 50th sample within validated chunks

    auto handler = [&](const uint32_t* data, uint32_t chunkSize, uint32_t overlapSize) {
        total_read_chunks++;

        // Estimate glue buffer usage based on chunk position patterns
        // This is a heuristic since we don't have access to internal state
        uint32_t chunk_start_in_buffer = ((total_read_chunks - 1) * READ_STRIDE) % (WRITE_CHUNK_SIZE * 8);
        if (chunk_start_in_buffer + READ_CHUNK_SIZE > WRITE_CHUNK_SIZE * 8) {
            glue_buffer_usage_estimate++;
        }

        // Perform validation on selected chunks
        if (total_read_chunks % VALIDATION_INTERVAL == 1 || total_read_chunks <= 5) {
            validation_chunks_checked++;
            uint32_t expected_start_value = (total_read_chunks - 1) * READ_STRIDE;

            // Check samples at regular intervals within the chunk
            for (uint32_t i = 0; i < chunkSize; i += SAMPLE_CHECK_INTERVAL) {
                validation_samples_checked++;
                uint32_t expected_value = expected_start_value + i;

                if (data[i] != expected_value) {
                    data_validation_passed = false;
                    std::cerr << "❌ Data validation failed at chunk " << total_read_chunks
                              << ", sample " << i << ": expected " << expected_value
                              << ", got " << data[i] << std::endl;
                    return;
                }
            }

            // Always check first and last samples of validated chunks
            if (chunkSize > 0) {
                validation_samples_checked++;
                if (data[0] != expected_start_value) {
                    data_validation_passed = false;
                    std::cerr << "❌ Data validation failed at chunk " << total_read_chunks
                              << ", first sample: expected " << expected_start_value
                              << ", got " << data[0] << std::endl;
                    return;
                }

                if (chunkSize > 1) {
                    validation_samples_checked++;
                    uint32_t expected_last = expected_start_value + chunkSize - 1;
                    if (data[chunkSize - 1] != expected_last) {
                        data_validation_passed = false;
                        std::cerr << "❌ Data validation failed at chunk " << total_read_chunks
                                  << ", last sample: expected " << expected_last
                                  << ", got " << data[chunkSize - 1] << std::endl;
                        return;
                    }
                }
            }
        }
    };

    ChunkProcessor<> processor(WRITE_CHUNK_SIZE, READ_CHUNK_SIZE, READ_OVERLAP, 8, handler);

    std::cout << "\nStarting high-volume processing..." << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();

    // Perform writes with sequential data
    uint32_t sample_counter = 0;
    uint32_t actual_writes = 0;

    for (uint32_t write_idx = 0; write_idx < EXPECTED_WRITES && sample_counter < TOTAL_SAMPLES; ++write_idx) {
        uint32_t* writePtr = processor.getWriteChunkPtr();

        uint32_t samples_to_write = std::min(WRITE_CHUNK_SIZE, TOTAL_SAMPLES - sample_counter);

        // Fill write chunk with sequential data
        for (uint32_t i = 0; i < samples_to_write; ++i) {
            writePtr[i] = sample_counter + i;
        }

        // Fill remaining space with padding if partial write
        for (uint32_t i = samples_to_write; i < WRITE_CHUNK_SIZE; ++i) {
            writePtr[i] = 0xDEADBEEF;  // Distinctive padding pattern
        }

        processor.commitWriteChunk();
        sample_counter += samples_to_write;
        actual_writes++;

        // Progress indicator for long-running test
        if (write_idx % 500 == 0) {
            std::cout << "Progress: " << (sample_counter * 100 / TOTAL_SAMPLES) << "% ("
                      << sample_counter << "/" << TOTAL_SAMPLES << " samples)" << std::endl;
        }
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    std::cout << "\nProcessing completed!" << std::endl;

    // Calculate performance metrics
    double throughput = (double)sample_counter / (duration.count() / 1000.0);  // samples/second
    double zero_copy_efficiency = (double)(total_read_chunks - glue_buffer_usage_estimate) / total_read_chunks * 100.0;
    double validation_coverage = (double)validation_samples_checked / (total_read_chunks * READ_CHUNK_SIZE) * 100.0;

    std::cout << "\n📊 High-Volume Performance Results:" << std::endl;
    std::cout << "===========================================" << std::endl;
    std::cout << "Samples processed: " << sample_counter << " / " << TOTAL_SAMPLES << std::endl;
    std::cout << "Actual write operations: " << actual_writes << " (expected: " << EXPECTED_WRITES << ")" << std::endl;
    std::cout << "Total read chunks generated: " << total_read_chunks << " (expected: ~" << EXPECTED_READ_CHUNKS << ")" << std::endl;
    std::cout << "Processing time: " << duration.count() << "ms" << std::endl;
    std::cout << "Throughput: " << std::fixed << std::setprecision(0) << throughput << " samples/second" << std::endl;
    std::cout << "Zero-copy efficiency: " << std::fixed << std::setprecision(1) << zero_copy_efficiency << "%" << std::endl;
    std::cout << "Glue buffer usage: " << glue_buffer_usage_estimate << " chunks ("
              << std::fixed << std::setprecision(1) << (double)glue_buffer_usage_estimate / total_read_chunks * 100.0 << "%)" << std::endl;
    std::cout << "Data validation coverage: " << std::fixed << std::setprecision(1) << validation_coverage << "%" << std::endl;
    std::cout << "Validation chunks checked: " << validation_chunks_checked << std::endl;
    std::cout << "Validation samples checked: " << validation_samples_checked << std::endl;
    std::cout << "Data validation result: " << (data_validation_passed ? "✅ PASSED" : "❌ FAILED") << std::endl;

    // Success criteria
    bool performance_acceptable = throughput > 1000000;  // At least 1M samples/second
    bool processing_complete = sample_counter >= TOTAL_SAMPLES;
    bool chunks_generated = total_read_chunks > 0;
    bool efficiency_good = zero_copy_efficiency > 90.0;  // At least 90% zero-copy

    std::cout << "\n🔍 Test Criteria:" << std::endl;
    std::cout << "Performance (>1M samples/sec): " << (performance_acceptable ? "✅ PASS" : "❌ FAIL") << std::endl;
    std::cout << "Processing complete: " << (processing_complete ? "✅ PASS" : "❌ FAIL") << std::endl;
    std::cout << "Chunks generated: " << (chunks_generated ? "✅ PASS" : "❌ FAIL") << std::endl;
    std::cout << "Zero-copy efficiency (>90%): " << (efficiency_good ? "✅ PASS" : "❌ FAIL") << std::endl;
    std::cout << "Data validation: " << (data_validation_passed ? "✅ PASS" : "❌ FAIL") << std::endl;

    if (data_validation_passed && performance_acceptable && processing_complete && chunks_generated && efficiency_good) {
        std::cout << "\n🎉 High-volume performance test PASSED!" << std::endl;
        return 0;
    } else {
        std::cout << "\n❌ High-volume performance test FAILED!" << std::endl;
        return 1;
    }
}
