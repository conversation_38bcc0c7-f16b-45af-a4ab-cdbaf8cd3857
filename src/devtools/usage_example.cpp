// Example showing how to integrate data_exporter.hpp in existing C++ source files
// This demonstrates the correct include path and usage patterns

#include "data_exporter.hpp"  // Include the header-only library
#include <vector>
#include <cstdint>

// Example function that could be added to any existing source file
void debugSignalProcessing() {
    // Example: Debug a signal processing pipeline
    std::vector<float> input_signal = {1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
    std::vector<float> filtered_signal = {0.8f, 1.6f, 2.4f, 3.2f, 4.0f};
    std::vector<int16_t> quantized_signal = {800, 1600, 2400, 3200, 4000};
    
    // Export debug data for measurement session 42
    int measurement_id = 42;
    
    DevTools::export_debug_data("signal_proc", "input", measurement_id,
                                input_signal.data(), input_signal.size());
    
    DevTools::export_debug_data("signal_proc", "filtered", measurement_id,
                                filtered_signal.data(), filtered_signal.size());
    
    DevTools::export_debug_data("signal_proc", "quantized", measurement_id,
                                quantized_signal.data(), quantized_signal.size());
}

// Example: Integration in a class method
class VideoProcessor {
private:
    std::vector<uint32_t> raw_samples_;
    int debug_session_id_;
    
public:
    VideoProcessor() : debug_session_id_(0) {}
    
    void processFrame(const uint32_t* samples, uint32_t count) {
        // Copy samples for processing
        raw_samples_.assign(samples, samples + count);
        
        // Export raw data for debugging
        DevTools::export_debug_data("video_proc", "raw_frame", debug_session_id_,
                                    raw_samples_.data(), raw_samples_.size());
        
        // ... actual processing code ...
        
        // Export processed results
        std::vector<float> processed_data(count);
        // ... fill processed_data ...
        
        DevTools::export_debug_data("video_proc", "processed_frame", debug_session_id_,
                                    processed_data.data(), processed_data.size());
        
        debug_session_id_++;
    }
};

// Note: This file is for demonstration only and is not compiled as part of the project
