#pragma once

#include <string>
#include <fstream>
#include <iostream>
#include <type_traits>
#include <cstdint>
#include <sys/stat.h>
#include <cerrno>
#include <cstring>

namespace DevTools {

/**
 * DataExporter - Header-only template-based debug data export utility
 * 
 * Provides a simple interface for exporting debug data arrays to binary files
 * for visualization and analysis. Files are organized by measurement sessions
 * and automatically typed based on the template parameter.
 * 
 * Features:
 * - Template-based design supporting any data type
 * - Automatic directory creation
 * - Type-aware filename generation
 * - Binary file export with error handling
 * - Header-only implementation for easy integration
 * 
 * Usage:
 *   std::vector<float> data = {1.0f, 2.0f, 3.0f};
 *   export_debug_data("test", "signal", 1, data.data(), data.size());
 *   // Creates: dev_debug/test_1_signal_float.bin
 */

namespace detail {
    /**
     * Get C++ type name as string for filename generation
     * Supports common numeric types used in signal processing
     */
    template<typename T>
    constexpr const char* getTypeName() {
        if constexpr (std::is_same_v<T, float>) {
            return "float";
        } else if constexpr (std::is_same_v<T, double>) {
            return "double";
        } else if constexpr (std::is_same_v<T, int8_t>) {
            return "int8_t";
        } else if constexpr (std::is_same_v<T, uint8_t>) {
            return "uint8_t";
        } else if constexpr (std::is_same_v<T, int16_t>) {
            return "int16_t";
        } else if constexpr (std::is_same_v<T, uint16_t>) {
            return "uint16_t";
        } else if constexpr (std::is_same_v<T, int32_t>) {
            return "int32_t";
        } else if constexpr (std::is_same_v<T, uint32_t>) {
            return "uint32_t";
        } else if constexpr (std::is_same_v<T, int64_t>) {
            return "int64_t";
        } else if constexpr (std::is_same_v<T, uint64_t>) {
            return "uint64_t";
        } else {
            return "unknown";
        }
    }

    /**
     * Ensure debug directory exists using POSIX mkdir for compatibility
     * @return true on success, false on error
     */
    inline bool ensureDebugDirectory() {
        // Check if directory already exists
        struct stat st;
        if (stat("dev_debug", &st) == 0) {
            if (S_ISDIR(st.st_mode)) {
                return true; // Directory already exists
            } else {
                std::cerr << "DataExporter: 'dev_debug' exists but is not a directory" << std::endl;
                return false;
            }
        }

        // Create directory with read/write/execute permissions for owner, read/execute for group and others
        if (mkdir("dev_debug", 0755) == 0) {
            return true;
        }

        // Check if it failed because directory already exists (race condition)
        if (errno == EEXIST) {
            return true;
        }

        std::cerr << "DataExporter: Failed to create dev_debug directory: " << strerror(errno) << std::endl;
        return false;
    }

    /**
     * Generate filename for debug data export
     * Format: ${prefixName}_${measurementId}_${dataName}_${typeName}.bin
     */
    template<typename T>
    inline std::string generateFilename(const std::string& prefixName, 
                                       const std::string& dataName,
                                       int measurementId) {
        return "dev_debug/" + prefixName + "_" + std::to_string(measurementId) + 
               "_" + dataName + "_" + getTypeName<T>() + ".bin";
    }
}

/**
 * Export debug data array to binary file
 * 
 * Creates a binary file in dev_debug/ directory with automatic filename generation
 * based on the provided parameters and data type. The file contains raw binary data
 * in little-endian format suitable for analysis tools.
 * 
 * @tparam T Data type (float, int32_t, uint16_t, etc.)
 * @param prefixName Prefix for grouping related measurements (e.g., "demod", "filter")
 * @param dataName Descriptive name for this data array (e.g., "input", "output", "coeffs")
 * @param measurementId Unique identifier for this measurement session
 * @param data Pointer to data array
 * @param elements Number of elements in the array
 * @return true on success, false on error
 * 
 * Example:
 *   std::vector<float> signal = {1.0f, 2.0f, 3.0f, 4.0f};
 *   export_debug_data("filter", "input", 42, signal.data(), signal.size());
 *   // Creates: dev_debug/filter_42_input_float.bin
 */
template<typename T>
bool export_debug_data(const std::string& prefixName, 
                      const std::string& dataName, const int measurementId,
                      const T* data, const uint32_t elements) {
    // Validate input parameters
    if (data == nullptr) {
        std::cerr << "DataExporter: Null data pointer provided" << std::endl;
        return false;
    }
    
    if (elements == 0) {
        std::cerr << "DataExporter: Zero elements specified" << std::endl;
        return false;
    }
    
    if (prefixName.empty() || dataName.empty()) {
        std::cerr << "DataExporter: Empty prefix or data name provided" << std::endl;
        return false;
    }

    // Ensure debug directory exists
    if (!detail::ensureDebugDirectory()) {
        return false;
    }

    // Generate filename
    std::string filename = detail::generateFilename<T>(prefixName, dataName, measurementId);
    
    // Open file for binary writing
    std::ofstream file(filename, std::ios::binary | std::ios::trunc);
    if (!file.is_open()) {
        std::cerr << "DataExporter: Failed to open file for writing: " << filename << std::endl;
        return false;
    }

    // Write raw binary data
    const uint32_t bytesToWrite = elements * sizeof(T);
    file.write(reinterpret_cast<const char*>(data), bytesToWrite);
    
    if (!file.good()) {
        std::cerr << "DataExporter: Failed to write data to file: " << filename << std::endl;
        file.close();
        return false;
    }

    // Close file immediately
    file.close();
    
    if (!file.good()) {
        std::cerr << "DataExporter: Error occurred while closing file: " << filename << std::endl;
        return false;
    }

    // Success - optionally log the export
    // std::cout << "DataExporter: Exported " << elements << " " << detail::getTypeName<T>() << " elements to " << filename << std::endl;
    
    return true;
}

} // namespace DevTools
