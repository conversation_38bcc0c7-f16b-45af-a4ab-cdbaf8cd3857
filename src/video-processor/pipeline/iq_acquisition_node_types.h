#pragma once

#include <vector>
#include <complex>
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

// Holds a segment of IQ samples with overlap guards around an effective region.
struct ComplexIQSegment {
  std::vector<std::complex<VSampleFloat>> data;
  uint32_t totalSamples;
  uint32_t effectiveSamples;
  uint32_t effectiveOffset;
  uint64_t effectiveStartPosition;
  uint32_t segmentIndex;
};

} // namespace IQVideoProcessor::Pipeline
