#pragma once

#include "../../../../signal-processing/sync-by-frwd/sync_by_frwd.h"

namespace IQVideoProcessor::Pipeline {

class VideoSyncDetector {
public:
  struct Config {
    VSampleFloat pulseSyncValue;
    VSampleFloat pulseThresholdTrigValueDelta;
    float pulseThresholdLength;
  };

  struct Result {
    float fallingFrontPosition{0};
    float risingFrontPosition{0};
    float centerPosition{0};
    float width{0};
  };

  explicit VideoSyncDetector(const VSampleFloat *data, uint32_t dataSize, SampleRate sampleRate, const Config &config);
  ~VideoSyncDetector() = default;

  bool findNext(float fromPosition);
  [[nodiscard]] const Result& getResult() const;

private:
  SignalProcessing::SyncByFrwd syncByFrwd_;
  Config config_;
  Result result_;
  SampleRate sampleRate_;
  float pulseMaxWidth_;
  float maxSearchPosition_;
  float foundPosition_{0};

  bool syncBy(int frontType, float fromPosition, float samples);
};

}
