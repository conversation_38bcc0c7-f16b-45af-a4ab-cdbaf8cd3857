#include "./pal-encoder.h"

namespace IQVideoProcessor::Pipeline::SyncEncoders {

PALSyncEncoder::PALSyncEncoder(const VideoSyncEncoder::Config& config): VideoSyncEncoder(config) {
  // PAL: 15.625 kHz horizontal => 64 µs per line
  // lineDistance_ = config.sampleRate / FH_PAL;  // 64 µs
  lineDistance_ = config.lineDistance;
  halfLineDistance_ = lineDistance_ * 0.5;        // 32 µs

  // Unstandard distances, appeared due to processing using center positions of impulse instead of falling fronts
  lastVerticalToEqualizingDistance_ = lineDistance_ * 0.30528;

  lastEqualizingToTopFieldHorizontalDistance_ = lineDistance_ * 0.517067;
  lastEqualizingToBottomFieldHorizontalDistance_ = lineDistance_ * 1.019230;
  lastEqualizingToVerticalDistance_ = lineDistance_ * 0.69230769;

  lastTopFieldHorizontalToEqualingDistance_ = lineDistance_ * 0.98125;
  lastBottomFieldHorizontalToEqualingDistance_ = lineDistance_ * 0.48557692;

  lineDistanceTolerance_ = lineDistance_ * 0.015;      // +-1.5%
  halfLineDistanceTolerance_ = lineDistanceTolerance_;  // keep same tol for half-line

  // TOP FIELD
  fillTopOrBottomFieldVerticalEncoders(topFieldVerticalStartIdx_, topFieldVerticalLastIdx_, true); // 5 vertical (broad) sync pulses
  fillTopFieldPostEqualizingEncoders(topFieldPostEqualizingStartIdx_);                             // 5 post-equalizing pulses
  fillTopOrBottomFieldHorizontalEncoders(topFieldHorizontalStartIdx_, true);                      // Horizontal lines (first non-video, then active)
  fillTopOrBottomFieldPreEqualizingEncoders(topFieldPreEqualizingStartIdx_, topFieldPreEqualizingLastIdx_, true); // 5 pre-equalizing pulses

  // BOTTOM FIELD
  fillTopOrBottomFieldVerticalEncoders(bottomFieldVerticalStartIdx_, bottomFieldVerticalLastIdx_, false); // 5 vertical (broad) sync pulses
  fillBottomFieldPostEqualizingEncoders(bottomFieldPostEqualizingStartIdx_);                               // 5 post-equalizing pulses
  fillTopOrBottomFieldHorizontalEncoders(bottomFieldHorizontalStartIdx_, false);                           // Horizontal lines
  fillTopOrBottomFieldPreEqualizingEncoders(bottomFieldPreEqualizingStartIdx_, bottomFieldPreEqualizingLastIdx_, false); // 5 pre-equalizing pulses
}

void PALSyncEncoder::transit(const Transition transition) {
  switch (transition) {
    case Transition::NEXT: return transitNext();
    case Transition::TO_TOP_FIELD_PRE_EQUALIZING_LAST: return setEncoderIdx(topFieldPreEqualizingLastIdx_);
    case Transition::TO_TOP_FIELD_VERTICAL_START: return setEncoderIdx(topFieldVerticalStartIdx_);
    case Transition::TO_TOP_FIELD_VERTICAL_LAST: return setEncoderIdx(topFieldVerticalLastIdx_);
    case Transition::TO_TOP_FIELD_HORIZONTAL_START: return setEncoderIdx(topFieldHorizontalStartIdx_);
    case Transition::TO_BOTTOM_FIELD_HORIZONTAL_START: return setEncoderIdx(bottomFieldHorizontalStartIdx_);
    default: break;
  }
}

// 5 long/broad vertical sync pulses per field in PAL.
// First 4 expect another vertical; the last expects an equalizing pulse.
void PALSyncEncoder::fillTopOrBottomFieldVerticalEncoders(uint32_t &startIdx, uint32_t &lastIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;

  const Range<double> nextVerticalSyncExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { VERTICAL_SYNC_PULSE, nextVerticalSyncExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 4; ++i) { // 4 first broad pulses
    encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::VERTICAL_SYNC, 0);
  }

  lastIdx = encoders.size();

  // Last broad pulse -> next is an equalizing pulse with a center-to-center offset
  distance = lastVerticalToEqualizingDistance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::VERTICAL_SYNC, 0);
}

// Top field post-equalizing: 5 pulses
void PALSyncEncoder::fillTopFieldPostEqualizingEncoders(uint32_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };

  // First 4 equalizing pulses
  for (auto i = 0; i < 4; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }

  // 5th equalizing -> next horizontal (either H/2 or H apart), used to determine/maintain field phase
  const auto toTopFieldDistance =  lastEqualizingToTopFieldHorizontalDistance_;
  const auto toBottomFieldDistance = lastEqualizingToBottomFieldHorizontalDistance_;
  const Range<double> nextTopFieldHorizontalExpectedDistanceRange{toTopFieldDistance - tolerance, toTopFieldDistance + tolerance};
  const Range<double> nextBottomFieldHorizontalExpectedDistanceRange{toBottomFieldDistance - tolerance, toBottomFieldDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextTopFieldHorizontalExpectedDistanceRange, toTopFieldDistance, Transition::NEXT }, // stay in top field phase
    { HORIZONTAL_SYNC_PULSE, nextBottomFieldHorizontalExpectedDistanceRange, toBottomFieldDistance, Transition::TO_BOTTOM_FIELD_HORIZONTAL_START }, // switch to bottom field phase
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

// Horizontal lines: initial non-video lines, then active video lines, then a line bridging to pre-equalizing.
void PALSyncEncoder::fillTopOrBottomFieldHorizontalEncoders(uint32_t &startIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = lineDistance_;
  const auto tolerance = lineDistanceTolerance_;

  // The first 17 expect to be followed with line distance horizontal pulse, and doesn't cary video information
  const Range<double> nextHorizontalExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextHorizontalExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 17; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  }

  // Active video lines per field: aim for 288 per field (576 per frame).
  constexpr auto impulses = 287;
  for (auto i = 0; i < impulses; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isTopField, possibleFutures, FrameRegion::HORIZONTAL_LINES, i);
  }

  if (isTopField) {
    // The last 288 of top field expect to be fully functional line, followed by equalizing pulse
    distance = lastTopFieldHorizontalToEqualingDistance_; // ~0.984375 * H
    const auto hlTolerance = halfLineDistanceTolerance_;
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - hlTolerance, distance + hlTolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  } else {
    // The last 288 of bottom field expect to be a half line distance, followed by equalizing pulse
    distance = lastBottomFieldHorizontalToEqualingDistance_; // ~0.484375 * H
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isTopField, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, impulses);
  }
}

// Pre-equalizing pulses before the vertical sync
void PALSyncEncoder::fillTopOrBottomFieldPreEqualizingEncoders(uint32_t &startIdx, uint32_t &lastIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 4; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::PRE_EQUALIZING, 0);
  }

  lastIdx = encoders.size();

  // Final pre-equalizing pulse -> next is a vertical (broad) sync pulse
  const auto toVerticalDistance = lastEqualizingToVerticalDistance_; // ~0.71875 * H
  const Range<double> lastVerticalExpectedDistanceRange{toVerticalDistance - tolerance, toVerticalDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { VERTICAL_SYNC_PULSE, lastVerticalExpectedDistanceRange, toVerticalDistance, Transition::NEXT },
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::PRE_EQUALIZING, 0);
}

// Bottom field post-equalizing
void PALSyncEncoder::fillBottomFieldPostEqualizingEncoders(uint32_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 4; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }

  // 5th equalizing -> next horizontal (H/2 or H), used to determine/maintain field phase
  const auto toBottomFieldDistance = lastEqualizingToBottomFieldHorizontalDistance_; // ~1.015625 * H
  const auto toTopFieldDistance =  lastEqualizingToTopFieldHorizontalDistance_;      // ~0.515625 * H

  const Range<double> nextBottomFieldHorizontalExpectedDistanceRange{toBottomFieldDistance - tolerance, toBottomFieldDistance + tolerance};
  const Range<double> nextTopFieldHorizontalExpectedDistanceRange{toTopFieldDistance - tolerance, toTopFieldDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextBottomFieldHorizontalExpectedDistanceRange, toBottomFieldDistance, Transition::NEXT },
    { HORIZONTAL_SYNC_PULSE, nextTopFieldHorizontalExpectedDistanceRange, toTopFieldDistance, Transition::TO_TOP_FIELD_HORIZONTAL_START },
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

} // namespace IQVideoProcessor::Pipeline::SyncEncoders
