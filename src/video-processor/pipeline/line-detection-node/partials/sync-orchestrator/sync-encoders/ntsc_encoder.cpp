#include "./ntsc-encoder.h"

namespace IQVideoProcessor::Pipeline::SyncEncoders {

NTSCSyncEncoder::NTSCSyncEncoder(const VideoSyncEncoder::Config& config): VideoSyncEncoder(config) {
  // lineDistance_ = config.sampleRate / FH_NTSC;   // ~63.5 µs
  lineDistance_ = config.lineDistance;
  halfLineDistance_ = lineDistance_ * 0.5;          // ~31.75 µs

  // Unstandard distances, appeared due to processing using center positions of impulse instead of falling fronts
  lastVerticalToEqualizingDistance_ = lineDistance_ * 0.30703;

  lastEqualizingToTopFieldHorizontalDistance_ = lineDistance_ * 0.5184353; // a little bit longer than H/2 (as we are processing using center position of impulse)
  lastEqualizingToBottomFieldHorizontalDistance_ = lineDistance_ * 1.017315486; // a little bit longer than H (as we are processing using center position of impulse)
  lastEqualizingToVerticalDistance_ = lineDistance_ * 0.6941391257;

  lastBottomFieldHorizontalToEqualingDistance_ = lineDistance_ * 0.9809474686; // a little bit shorter than H (as we are processing using center position of impulse)
  lastTopFieldHorizontalToEqualingDistance_ = lineDistance_ * 0.4805613143; // a little bit shorter than H/2 (as we are processing using center position of impulse)

  lineDistanceTolerance_ = lineDistance_ * 0.015; // +-1.5%;
  halfLineDistanceTolerance_ = lineDistanceTolerance_; // +-1.5%; for now

  // TOP FIELD
  fillTopOrBottomFieldVerticalEncoders(topFieldVerticalStartIdx_, topFieldVerticalLastIdx_, true); // First 6 vertical sync pulses in the top field
  fillTopFieldPostEqualizingEncoders(topFieldPostEqualizingStartIdx_); // Then 6 equalizing pulses in the top field
  fillTopOrBottomFieldHorizontalEncoders(topFieldHorizontalStartIdx_, true); // Then 253 horizontal pulses in the top field
  fillTopOrBottomFieldPreEqualizingEncoders(topFieldPreEqualizingStartIdx_, topFieldPreEqualizingLastIdx_, true); // Then 6 equalizing pulses in the top field
  // BOTTOM FIELD
  fillTopOrBottomFieldVerticalEncoders(bottomFieldVerticalStartIdx_, bottomFieldVerticalLastIdx_, false); // First 6 vertical sync pulses in the bottom field
  fillBottomFieldPostEqualizingEncoders(bottomFieldPostEqualizingStartIdx_); // Then 6 equalizing pulses in the bottom field
  fillTopOrBottomFieldHorizontalEncoders(bottomFieldHorizontalStartIdx_, false); // Then 253 horizontal pulses in the bottom field
  fillTopOrBottomFieldPreEqualizingEncoders(bottomFieldPreEqualizingStartIdx_, bottomFieldPreEqualizingLastIdx_, false);
}

void NTSCSyncEncoder::transit(const Transition transition) {
  switch (transition) {
    case Transition::NEXT: return transitNext();
    case Transition::TO_TOP_FIELD_PRE_EQUALIZING_LAST: return setEncoderIdx(topFieldPreEqualizingLastIdx_);
    case Transition::TO_TOP_FIELD_VERTICAL_START: return setEncoderIdx(topFieldVerticalStartIdx_);
    case Transition::TO_TOP_FIELD_VERTICAL_LAST: return setEncoderIdx(topFieldVerticalLastIdx_);
    case Transition::TO_TOP_FIELD_HORIZONTAL_START: return setEncoderIdx(topFieldHorizontalStartIdx_);
    case Transition::TO_BOTTOM_FIELD_HORIZONTAL_START: return setEncoderIdx(bottomFieldHorizontalStartIdx_);
    default: break;
  }
}

void NTSCSyncEncoder::fillTopOrBottomFieldVerticalEncoders(uint32_t &startIdx, uint32_t &lastIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;

  // The first 5 expect to be followed only by another vertical sync pulse
  const Range<double> nextVerticalSyncExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { VERTICAL_SYNC_PULSE, nextVerticalSyncExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::VERTICAL_SYNC, 0);
  }

  lastIdx = encoders.size();

  // The last one expects to be followed by an equalizing pulse
  distance = lastVerticalToEqualizingDistance_;
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  encoders.emplace_back(VERTICAL_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::VERTICAL_SYNC, 0);
}

void NTSCSyncEncoder::fillTopFieldPostEqualizingEncoders(uint32_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }
  // The 6th, depend on top/bottom field, expects to be followed by half or full line distance horizontal pulse
  const auto toTopFieldDistance =  lastEqualizingToTopFieldHorizontalDistance_;
  const auto toBottomFieldDistance = lastEqualizingToBottomFieldHorizontalDistance_;

  const Range<double> nextTopFieldHorizontalExpectedDistanceRange{toTopFieldDistance - tolerance, toTopFieldDistance + tolerance};
  const Range<double> nextBottomFieldHorizontalExpectedDistanceRange{toBottomFieldDistance - tolerance, toBottomFieldDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextTopFieldHorizontalExpectedDistanceRange, toTopFieldDistance, Transition::NEXT }, // The next 6th pulse is equalizing, and we are still in the top field, continue
    { HORIZONTAL_SYNC_PULSE, nextBottomFieldHorizontalExpectedDistanceRange, toBottomFieldDistance, Transition::TO_BOTTOM_FIELD_HORIZONTAL_START }, // We are switching to bottom field phase
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, true, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

void NTSCSyncEncoder::fillTopOrBottomFieldHorizontalEncoders(uint32_t &startIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  auto distance = lineDistance_;
  const auto tolerance = lineDistanceTolerance_;
  // The first 13 expect to be followed with line distance horizontal pulse, and doesn't cary video information
  const Range<double> nextHorizontalExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextHorizontalExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 13; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  }

  const auto impulses = isTopField ? 240 : 239;
  // The next 240->top field / 239->bottom field expect to be followed with line distance horizontal pulse, and carry video information
  for (auto i = 0; i < impulses; ++i) {
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isTopField, possibleFutures, FrameRegion::HORIZONTAL_LINES, i);
  }

  if (isTopField) {
    // The last 241st is invisible, half-line distance horizontal region, followed be equalizing pulse
    distance = lastTopFieldHorizontalToEqualingDistance_; // half line distance to the next equalizing pulse
    const auto hlTolerance = halfLineDistanceTolerance_;
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - hlTolerance, distance + hlTolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, 0);
  } else {
    // The last 240th expect to be followed with line distance pre equalizing pulse, and carry video information
    distance = lastBottomFieldHorizontalToEqualingDistance_;
    const Range<double> nextPreEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
    const std::vector<EIFuture> lastPossibleFutures = {
      { EQUALIZING_SYNC_PULSE, nextPreEqualizingExpectedDistanceRange, distance, Transition::NEXT },
    };
    encoders.emplace_back(HORIZONTAL_SYNC_PULSE, true, isTopField, lastPossibleFutures, FrameRegion::HORIZONTAL_LINES, impulses);
  }
}

void NTSCSyncEncoder::fillTopOrBottomFieldPreEqualizingEncoders(uint32_t &startIdx, uint32_t &lastIdx, bool isTopField) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, isTopField, possibleFutures, FrameRegion::PRE_EQUALIZING, 0);
  }

  lastIdx = encoders.size();

  const auto toVerticalDistance = lastEqualizingToVerticalDistance_;
  const Range<double> lastVerticalExpectedDistanceRange{toVerticalDistance - tolerance, toVerticalDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { VERTICAL_SYNC_PULSE, lastVerticalExpectedDistanceRange, toVerticalDistance, Transition::NEXT }, // We are switching to bottom field phase
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, isTopField, lastPossibleFutures, FrameRegion::PRE_EQUALIZING, 0);
}

void NTSCSyncEncoder::fillBottomFieldPostEqualizingEncoders(uint32_t &startIdx) {
  auto& encoders = getWritableEncoders();
  startIdx = encoders.size();

  const auto distance = halfLineDistance_;
  const auto tolerance = halfLineDistanceTolerance_;
  // The first 5 expect to be followed with half line distance equalizing pulse
  const Range<double> nextEqualizingExpectedDistanceRange{distance - tolerance, distance + tolerance};
  const std::vector<EIFuture> possibleFutures = {
    { EQUALIZING_SYNC_PULSE, nextEqualizingExpectedDistanceRange, distance, Transition::NEXT },
  };
  for (auto i = 0; i < 5; ++i) {
    encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, possibleFutures, FrameRegion::POST_EQUALIZING, 0);
  }
  // The 6th, depend on top/bottom field, expects to be followed by half or full line distance horizontal pulse
  const auto toBottomFieldDistance = lastEqualizingToBottomFieldHorizontalDistance_;
  const auto toTopFieldDistance =  lastEqualizingToTopFieldHorizontalDistance_;

  const Range<double> nextBottomFieldHorizontalExpectedDistanceRange{toBottomFieldDistance - tolerance, toBottomFieldDistance + tolerance};
  const Range<double> nextTopFieldHorizontalExpectedDistanceRange{toTopFieldDistance - tolerance, toTopFieldDistance + tolerance};
  const std::vector<EIFuture> lastPossibleFutures = {
    { HORIZONTAL_SYNC_PULSE, nextBottomFieldHorizontalExpectedDistanceRange, toBottomFieldDistance, Transition::NEXT },
    { HORIZONTAL_SYNC_PULSE, nextTopFieldHorizontalExpectedDistanceRange, toTopFieldDistance, Transition::TO_TOP_FIELD_HORIZONTAL_START },
  };
  encoders.emplace_back(EQUALIZING_SYNC_PULSE, false, false, lastPossibleFutures, FrameRegion::POST_EQUALIZING, 0);
}

}
