#pragma once
#include "../video_sync_pulse.h"
#include <optional>

namespace IQVideoProcessor::Pipeline {

class SyncPulseLocator {
public:
  explicit SyncPulseLocator(const std::vector<VideoSyncPulse>& pulses, const uint32_t fromIdx): pulses(pulses), fromIdx_(fromIdx) {

  }
  ~SyncPulseLocator() = default;

  std::optional<const VideoSyncPulse*> findNextInRangeByAbsCenterPosition(const double fromAbsCenterPosition, const double toAbsCenterPosition) {
    // There will be no cases, when fromAbsCenterPosition for each next search is less than for the previous search
    // because fromAbsCenterPosition is always previous found pulse absCenterPosition, so we can continue search from fromIdx_
    // instead of starting from 0 each time
    auto fromIdx = fromIdx_;
    while (fromIdx < pulses.size() && pulses[fromIdx].absCenterPosition < fromAbsCenterPosition) {
      ++fromIdx;
    }
    if (fromIdx_ >= pulses.size()) return std::nullopt;

    if (const auto &pulse = pulses[fromIdx]; pulse.absCenterPosition <= toAbsCenterPosition) {
      fromIdx_ = fromIdx + 1; // next search will start from the pulse located after this one
      return &pulse;
    }
    fromIdx_ = fromIdx; // next search will start from the same pulse located after fromAbsCenterPosition
    return std::nullopt;
  }

private:
  const std::vector<VideoSyncPulse>& pulses;
  uint32_t fromIdx_;
};

}
