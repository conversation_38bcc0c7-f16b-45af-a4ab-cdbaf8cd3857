#include "./sync-orchestrator.h"
#include <iostream>
#include "./sync-encoders/pal-encoder.h"
#include "./sync-encoders/ntsc-encoder.h"
#include "logging/logging.h"

namespace IQVideoProcessor::Pipeline {

SyncOrchestrator::SyncOrchestrator(const SampleRate sampleRate) : sampleRate_(sampleRate) {

}

void SyncOrchestrator::initialize(const VideoStandardDetector::Result& detectedStandard, EventCallback eventCallback) {
  detectedStandard_ = detectedStandard;
  callback_ = std::move(eventCallback);

  if (detectedStandard_.standard == UNKNOWN_VIDEO_STANDARD) {
    LOG_ERROR(SYNC_ORCHESTRATOR, "invalid video standard received");
    initialized_ = false;
    return;
  }

  if (detectedStandard_.lineDistance <= 0) {
    LOG_ERROR(SYNC_ORCHESTRATOR, "invalid horizontal line duration received: " << detectedStandard_.lineDistance);
    initialized_ = false;
    return;
  }

  VideoSyncEncoder::Config config {
    .standard = detectedStandard_.standard,
    .lineDistance = detectedStandard_.lineDistance,
  };

  if (detectedStandard_.standard == PAL_STANDARD) {
    videoSyncEncoder_ = std::make_unique<PALSyncEncoder>(config);
  } else if (detectedStandard_.standard == NTSC_STANDARD) {
    videoSyncEncoder_ = std::make_unique<NTSCSyncEncoder>(config);
  } else {
    LOG_ERROR(SYNC_ORCHESTRATOR, "unsupported video standard: " << detectedStandard_.standard);
    initialized_ = false;
    return;
  }

  uninterruptedFrameCount_ = 0;
  resetCounters();

  initialized_ = true;
}

bool SyncOrchestrator::initialized() const {
  return initialized_;
}

void SyncOrchestrator::reset() {
  LOG_VERBOSE(SYNC_ORCHESTRATOR, "resetting");
  if (videoSyncEncoder_) {
    videoSyncEncoder_->reset();
  }
  locked_ = false;
  uninterruptedFrameCount_ = 0;
  resetCounters();
}

void SyncOrchestrator::resetCounters() {
  matchedPulseCounter_.stop(); // Not running until reset with first unmatched pulse
  unmatchedPulseCounter_.reset(); // Always running
}

void SyncOrchestrator::process(const std::vector<VideoSyncPulse>& pulses, const double processingEndPosition) {
  if (!initialized_ || !videoSyncEncoder_) return;

  uint32_t fromPulsesIdx = 0;
  if (!locked_) {
    if (const auto [locked, lockedPulseIdx] = tryLockOnVertical(pulses); locked) {
      LOG_VERBOSE(SYNC_ORCHESTRATOR, "initial vertical lock acquired");
      fromPulsesIdx = lockedPulseIdx + 1; // Start processing from the pulse we locked on
      currentProcessingPosition_ = pulses[lockedPulseIdx].absCenterPosition;
      locked_ = true;
      emitEvent(LOCKED, currentProcessingPosition_, currentProcessingPosition_, false, 0);
    } else {
      LOG_VERBOSE(SYNC_ORCHESTRATOR, "trying to lock ...");
      return; // Still not locked, wait for next segment
    }
  }

  SyncPulseLocator pulseLocator(pulses, fromPulsesIdx);
  while (locked_ && canProcessCurrentEncoderItem(processingEndPosition)) {
    // We have enough room/samples to process the current video sync encoder item
    processCurrentEncoderItem(pulseLocator);
  }

}

std::tuple<bool, uint32_t> SyncOrchestrator::tryLockOnVertical(const std::vector<VideoSyncPulse>& pulses) const {
  const VideoSyncPulse *previousPulse = nullptr;
  EstimatedPulseType prevPulseType = ES_UNKNOWN_SYNC_PULSE;

  auto lockOn = [this](const Transition transition, const uint32_t processed) -> std::tuple<bool, uint32_t> {
    videoSyncEncoder_->reset();
    videoSyncEncoder_->transit(transition);
    return { true, processed };
  };

  for (uint32_t processed = 0; processed < pulses.size(); ++processed ) {
    auto& pulse = pulses[processed];
    const auto pulseType = rangeEstimator_.estimatePulseByWidth(pulse.width);
    if (previousPulse == nullptr) {
      previousPulse = &pulse;
      prevPulseType = pulseType;
      continue;
    }
    const auto distance = pulse.absCenterPosition - previousPulse->absCenterPosition;
    // Look for the pattern: Equalizing -> Vertical, about 70% of horizontal line distance
    if (
      prevPulseType == ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE
      && pulseType == ES_VERTICAL_SYNC_PULSE &&
      rangeEstimator_.is70PercentHorizontalLineDistance(distance)) {
      return lockOn(Transition::TO_TOP_FIELD_VERTICAL_START, processed); // Found vertical sync pulse, lock on
    }
    // Look for the pattern: Vertical -> Equalizing, about 33% of horizontal line distance
    if (prevPulseType == ES_VERTICAL_SYNC_PULSE &&
      pulseType == ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE &&
      rangeEstimator_.is33PercentHorizontalLineDistance(distance)) {
      return lockOn(Transition::TO_TOP_FIELD_VERTICAL_LAST, processed - 1); // Found equalizing after vertical, lock on previous vertical
    }

    previousPulse = &pulse;
    prevPulseType = pulseType;
  }

  return { false, pulses.size() - 1 };
}

inline EstimatedPulseType SyncOrchestrator::toEstimatedPulseType(const VideoSyncPulseType pulseType) {
  switch (pulseType) {
  case HORIZONTAL_SYNC_PULSE:
  case EQUALIZING_SYNC_PULSE:
    return ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE;
  case VERTICAL_SYNC_PULSE:
    return ES_VERTICAL_SYNC_PULSE;
  case UNKNOWN_PULSE:
    return ES_UNKNOWN_SYNC_PULSE;
  default:
    throw std::invalid_argument("unknown pulse type"); // This MUST throw an exception!
  }
}

inline bool SyncOrchestrator::canProcessCurrentEncoderItem(const double processingEndPosition) const {
  return currentProcessingPosition_ + videoSyncEncoder_->currentItem().maxFutureDistance <= processingEndPosition;
}

inline void SyncOrchestrator::processDefaultFuture() {
  const auto& encoderItem = videoSyncEncoder_->currentItem();
  const auto defaultFuture = encoderItem.futures[encoderItem.defaultFutureIdx];
  processEncoderTransition(defaultFuture.transition, currentProcessingPosition_ + defaultFuture.defaultDistance);
}

inline void SyncOrchestrator::processEncoderTransition(const Transition transition, const double nextProcessingPosition) {
  const auto currentProcessingPosition = currentProcessingPosition_;
  const auto& currentItem = videoSyncEncoder_->currentItem();

  // Apply the transition first to get the next item
  currentProcessingPosition_ = nextProcessingPosition;
  videoSyncEncoder_->transit(transition);
  const auto& nextItem = videoSyncEncoder_->currentItem();

  // Then check for equalization
  if (currentItem.pulseType == EQUALIZING_SYNC_PULSE && (currentItem.region == FrameRegion::POST_EQUALIZING || currentItem.region == FrameRegion::PRE_EQUALIZING)) {
    emitEvent(EQUALIZATION_DETECTED, currentProcessingPosition, nextProcessingPosition, currentItem.topField, 0);
  }
  // Finally check for visible lines
  if (currentItem.visible && currentItem.region == FrameRegion::HORIZONTAL_LINES) {
    emitEvent(LINE_DETECTED, currentProcessingPosition, nextProcessingPosition, currentItem.topField, currentItem.visibleLineIndex);
  }
  // Check for frame field begin/end
  if ((currentItem.region != FrameRegion::HORIZONTAL_LINES) && (nextItem.region == FrameRegion::HORIZONTAL_LINES)) {
    uninterruptedFrameCount_++;
    emitEvent(FRAME_FIELD_BEGIN, currentProcessingPosition, currentProcessingPosition, nextItem.topField, 0);
  }
  if ((currentItem.region == FrameRegion::HORIZONTAL_LINES) && (nextItem.region != FrameRegion::HORIZONTAL_LINES)) {
    emitEvent(FRAME_FIELD_END, currentProcessingPosition, currentProcessingPosition, currentItem.topField, currentItem.visibleLineIndex);
  }
}

inline void SyncOrchestrator::processCurrentEncoderItem(SyncPulseLocator &pulseLocator) {
  const auto& encoderItem = videoSyncEncoder_->currentItem();
  // Look for the next pulse in range
  const auto searchResult = pulseLocator.findNextInRangeByAbsCenterPosition(currentProcessingPosition_, currentProcessingPosition_ + encoderItem.maxFutureDistance);
  if (!searchResult.has_value()) { // No matching pulse found in range, processing the default future, automatically predicted by encoder
    return processDefaultFuture();
  }
  // Found a matching pulse, process it
  const auto& currentProcessingPulse = *(searchResult.value());
  const auto distance = currentProcessingPulse.absCenterPosition - currentProcessingPosition_;
  if (distance <= 0) {
    return processDefaultFuture(); // Should not happen, but just in case
  }
  // Check if any of the futures match this pulse by type and distance
  const auto pulseType = rangeEstimator_.estimatePulseByWidth(currentProcessingPulse.width);
  for (const auto& future : encoderItem.futures) {
    if (toEstimatedPulseType(future.pulseType) == pulseType && future.inRange.inRange(distance)) {
      // Expected pulse found - incrementing the series counter if running
      if (matchedPulseCounter_.running() && matchedPulseCounter_.tick()) {
        resetCounters(); // We have enough matched pulses to keep the lock, reset the counters
      }
      // TODO Nice, now we can evaluate the phase correction, but skip it for now
      // const auto deltaDistance = currentProcessingPulse.absCenterPosition - (currentProcessingPosition_ + future.defaultDistance);
      // std:: cout << "SyncOrchestrator: Phase correction of " << deltaDistance << " samples applied at position " << currentProcessingPulse.absCenterPosition << std::endl;
      return processEncoderTransition(future.transition, currentProcessingPulse.absCenterPosition); // Move exactly to the pulse center position
    }
  }

  // We have a pulse, but it doesn't match any future by type/distance. This means we are out of sync, or receive some unpredicted pulse.
  matchedPulseCounter_.reset(); // Invalid pulse found - starting the matched pulses counter check anew
  if (unmatchedPulseCounter_.tick()) {
    // Bad counter reached its limit - we are out of sync, need to re-lock
    LOG_WARNING(SYNC_ORCHESTRATOR, "out of sync detected, resetting the sync lock");
    handleLockLoss();
    return; // Exit processing to force re-locking
  }

  LOG_WARNING(SYNC_ORCHESTRATOR, "unmatched pulse detected, processing default sync prediction");

  return processDefaultFuture();
}

inline void SyncOrchestrator::emitEvent(
  const EventType type,
  const double fromPosition,
  const double toPosition,
  const bool isTopField,
  const uint32_t lineNumber
) const {
  if (!callback_) return;

  const EventData event{
    .standard = detectedStandard_.standard,
    .type = type,
    .isTopField = isTopField,
    .lineDistance_ = detectedStandard_.lineDistance,
    .fromAbsPosition_ = fromPosition,
    .toAbsPosition_ = toPosition,
    .lineNumber = lineNumber,
    .uninterruptedFieldSeries = uninterruptedFrameCount_
  };

  callback_(event);
}

inline void SyncOrchestrator::handleLockLoss() {
  locked_ = false;
  uninterruptedFrameCount_ = 0;
  resetCounters();
  emitEvent(LOCK_LOST, 0, 0, false, 0);
}

}
