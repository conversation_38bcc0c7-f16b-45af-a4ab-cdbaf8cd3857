#pragma once
#include "../../../video_standard.h"
#include "./signal_range_estimator.h"
#include "./video_sync_pulse.h"
#include <cstddef>
#include <cstdint>
#include <limits>
#include <vector>

namespace IQVideoProcessor::Pipeline {

class VideoStandardDetector {
public:
  enum DetectionStatus {
    DETECTION_IN_PROGRESS = 0,
    DETECTION_COMPLETE = 10,
    DETECTION_FAILED = 20,
  };

  struct Result {
    VideoStandard standard{UNKNOWN_VIDEO_STANDARD};
    DetectionStatus status{DETECTION_IN_PROGRESS};
    double lineDistance{0.0};
  };

  explicit VideoStandardDetector(SampleRate sampleRate);

  [[nodiscard]] Result& processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses);

  void reset();

private:
  enum class SyncType : uint8_t {
    Unknown = 0,
    Horizontal,
    Vertical,
    Equalizing
  };

  struct VerticalBurst {
    uint32_t equalizingBefore{0};
    uint32_t verticalCount{0};
    uint32_t equalizingAfter{0};
  };

  struct HorizontalEstimate {
    bool valid{false};
    double meanSamples{0.0};
    double deviationSamples{0.0};
    double relativeError{std::numeric_limits<double>::infinity()};
    VideoStandard dominantStandard{UNKNOWN_VIDEO_STANDARD};
    uint32_t sampleCount{0};
  };

  static constexpr uint32_t MIN_PULSES_FOR_ANALYSIS = 600;
  static constexpr uint32_t MAX_BUFFERED_PULSES = 2000;
  static constexpr uint32_t MAX_SEGMENTS_WITHOUT_PROGRESS = 15;
  static constexpr uint32_t MIN_HORIZONTAL_INTERVALS_FOR_ESTIMATE = 120;
  static constexpr double MAX_HORIZONTAL_REL_ERROR = 0.015;
  static constexpr double MAX_HORIZONTAL_REL_STD = 0.0075;
  static constexpr double STRICT_FREQ_TOLERANCE = 0.01;
  static constexpr int MIN_CONFIDENCE_SCORE = 6;
  static constexpr int SCORE_MARGIN_REQUIRED = 2;

  SampleRate sampleRate_{0};
  SignalRangeEstimator signalRangeEstimator_;
  Result result_{};
  uint32_t processedSegments_{0};
  bool analysisAttempted_{false};
  std::vector<VideoSyncPulse> collectedPulses_;

  void trimBufferIfNeeded();
  [[nodiscard]] bool readyForAnalysis() const;
  void analyze();

  void classifyPulses(const std::vector<VideoSyncPulse>& pulses, std::vector<SyncType>& outTypes) const;
  [[nodiscard]] SyncType classifyPulse(const VideoSyncPulse* prev, const VideoSyncPulse& current, const VideoSyncPulse* next) const;
  [[nodiscard]] HorizontalEstimate estimateHorizontalLineDistance(const std::vector<VideoSyncPulse>& pulses, const std::vector<SyncType>& types) const;
  [[nodiscard]] std::vector<VerticalBurst> extractVerticalBursts(const std::vector<VideoSyncPulse>& pulses, const std::vector<SyncType>& types) const;
  [[nodiscard]] static VideoStandard inferStandard(
    const HorizontalEstimate& hEstimate,
    const std::vector<VerticalBurst>& bursts,
    const std::vector<SyncType>& types
  );
};

} // namespace IQVideoProcessor::Pipeline
