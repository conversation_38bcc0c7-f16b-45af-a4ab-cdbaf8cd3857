#include "../../../video_processor_configs.h"
#include "./video_sync_detector.h"

namespace IQVideoProcessor::Pipeline {

VideoSyncDetector::VideoSyncDetector(
  const VSampleFloat *data,
  const uint32_t dataSize,
  const SampleRate sampleRate,
  const Config &config
) : syncByFrwd_(data, dataSize), config_(config), sampleRate_(sampleRate), maxSearchPosition_(static_cast<float>(dataSize - 1)) {
  pulseMaxWidth_ = static_cast<float>(sampleRate_) * static_cast<float>(PULSE_MAX_WIDTH_US); // 500 microseconds
}

[[nodiscard]] const VideoSyncDetector::Result &VideoSyncDetector::getResult() const {
  return result_;
}

bool VideoSyncDetector::findNext(VSampleFloat fromPosition) {
  while (fromPosition < maxSearchPosition_) {
    if (!syncBy(-1, fromPosition, 0)) { // search to EOF
      return false;
    }
    result_.fallingFrontPosition = foundPosition_;
    if (!syncBy(+1, foundPosition_, pulseMaxWidth_)) {
      fromPosition = foundPosition_;
      continue;
    }
    result_.risingFrontPosition = foundPosition_;
    result_.centerPosition = (result_.fallingFrontPosition + result_.risingFrontPosition) / static_cast<float>(2);
    result_.width = result_.risingFrontPosition - result_.fallingFrontPosition;
    return true;
  }
  return false;
}

bool VideoSyncDetector::syncBy(const int frontType, const float fromPosition, const float samples) {
  const auto result = syncByFrwd_(
    samples == 0, // search entire range if samples is 0
    frontType,
    config_.pulseSyncValue, // searched value
    fromPosition, // from position
    samples, // elements
    config_.pulseThresholdLength, // threshold size
    config_.pulseThresholdTrigValueDelta // threshold trig delta
  );
  if (!result.has_value()) {
    return false;
  }
  foundPosition_ = result.value();
  return true;
}

} // namespace IQVideoProcessor
