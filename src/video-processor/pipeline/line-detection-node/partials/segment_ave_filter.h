#pragma once
#include "../../iq_demodulation_node_types.h"

namespace IQVideoProcessor::Pipeline {

struct AveFilteredDemodulatedSegment: DemodulatedSegment {
  uint32_t aveSize = 0;
  uint32_t halfAveSize = 0;
};

class SegmentAveFilter final {
public:
  explicit SegmentAveFilter(SampleRate sampleRate);
  ~SegmentAveFilter() = default;
  const AveFilteredDemodulatedSegment& process(const DemodulatedSegment &segment);

private:
  SampleRate sampleRate_;
  AveFilteredDemodulatedSegment _ds500kHz_;
};

}
