#include "./video_standard_detector.h"
#include <algorithm>
#include <cmath>
#include <cstdlib>
#include <limits>
#include <numeric>

namespace IQVideoProcessor::Pipeline {

VideoStandardDetector::VideoStandardDetector(const SampleRate sampleRate)
  : sampleRate_(sampleRate),
    signalRangeEstimator_(sampleRate) {
  collectedPulses_.reserve(MAX_BUFFERED_PULSES);
  reset();
}

void VideoStandardDetector::reset() {
  result_ = Result{};
  result_.lineDistance = 0.0;
  processedSegments_ = 0;
  analysisAttempted_ = false;
  collectedPulses_.clear();
}

VideoStandardDetector::Result&
VideoStandardDetector::processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses) {
  if (result_.status != DETECTION_IN_PROGRESS) {
    return result_;
  }

  ++processedSegments_;

  if (!syncPulses.empty()) {
    collectedPulses_.insert(collectedPulses_.end(), syncPulses.begin(), syncPulses.end());
    trimBufferIfNeeded();

    if (readyForAnalysis()) {
      analyze();
    }
  }

  if (processedSegments_ >= MAX_SEGMENTS_WITHOUT_PROGRESS) {
    const bool insufficientSignal = collectedPulses_.size() < MIN_PULSES_FOR_ANALYSIS;
    if (insufficientSignal || analysisAttempted_) {
      result_.status = DETECTION_FAILED;
    }
  }

  return result_;
}

void VideoStandardDetector::trimBufferIfNeeded() {
  if (collectedPulses_.size() <= MAX_BUFFERED_PULSES) {
    return;
  }
  const uint32_t overflow = collectedPulses_.size() - MAX_BUFFERED_PULSES;
  collectedPulses_.erase(collectedPulses_.begin(), collectedPulses_.begin() + overflow);
}

bool VideoStandardDetector::readyForAnalysis() const {
  return collectedPulses_.size() >= MIN_PULSES_FOR_ANALYSIS;
}

void VideoStandardDetector::analyze() {
  const auto& pulses = collectedPulses_;
  if (pulses.size() < MIN_PULSES_FOR_ANALYSIS) {
    return;
  }

  analysisAttempted_ = true;

  std::vector<SyncType> types(pulses.size(), SyncType::Unknown);
  classifyPulses(pulses, types);

  const HorizontalEstimate hEstimate = estimateHorizontalLineDistance(pulses, types);
  result_.lineDistance = hEstimate.valid ? hEstimate.meanSamples : 0.0;

  const auto bursts = extractVerticalBursts(pulses, types);
  const VideoStandard standard = inferStandard(hEstimate, bursts, types);

  if (hEstimate.valid && standard != UNKNOWN_VIDEO_STANDARD) {
    result_.standard = standard;
    result_.status = DETECTION_COMPLETE;
  }
}

void VideoStandardDetector::classifyPulses(const std::vector<VideoSyncPulse>& pulses, std::vector<SyncType>& outTypes) const {
  const uint32_t count = pulses.size();
  for (uint32_t i = 0; i < count; ++i) {
    const VideoSyncPulse* prev = (i > 0) ? &pulses[i - 1] : nullptr;
    const VideoSyncPulse* next = (i + 1 < count) ? &pulses[i + 1] : nullptr;
    outTypes[i] = classifyPulse(prev, pulses[i], next);
  }
}

VideoStandardDetector::SyncType VideoStandardDetector::classifyPulse(
  const VideoSyncPulse* prev,
  const VideoSyncPulse& current,
  const VideoSyncPulse* next) const
{
  const bool hasPrev = prev != nullptr;
  const bool hasNext = next != nullptr;

  const auto widthType = signalRangeEstimator_.estimatePulseByWidth(current.width);
  if (widthType == ES_VERTICAL_SYNC_PULSE || widthType == ES_VERTICAL_LONG_SYNC_PULSE) {
    return SyncType::Vertical;
  }

  const double prevDistance = hasPrev ? current.absCenterPosition - prev->absCenterPosition : 0.0;
  const double nextDistance = hasNext ? next->absCenterPosition - current.absCenterPosition : 0.0;

  const bool prevHalf = hasPrev && signalRangeEstimator_.isHalfHorizontalLineDistance(prevDistance);
  const bool nextHalf = hasNext && signalRangeEstimator_.isHalfHorizontalLineDistance(nextDistance);

  if (prevHalf || nextHalf) {
    return SyncType::Equalizing;
  }

  const bool prevFull = hasPrev && signalRangeEstimator_.isHorizontalLineDistance(prevDistance);
  const bool nextFull = hasNext && signalRangeEstimator_.isHorizontalLineDistance(nextDistance);

  if (prevFull || nextFull) {
    return SyncType::Horizontal;
  }

  if (widthType == ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE) {
    return SyncType::Horizontal;
  }

  return SyncType::Unknown;
}

VideoStandardDetector::HorizontalEstimate VideoStandardDetector::estimateHorizontalLineDistance(const std::vector<VideoSyncPulse>& pulses, const std::vector<SyncType>& types) const {
  std::vector<uint32_t> horizontalIndices;
  horizontalIndices.reserve(pulses.size());

  for (uint32_t i = 0; i < pulses.size(); ++i) {
    if (types[i] == SyncType::Horizontal) {
      horizontalIndices.push_back(i);
    }
  }

  if (horizontalIndices.size() < MIN_HORIZONTAL_INTERVALS_FOR_ESTIMATE) {
    constexpr HorizontalEstimate invalid{};
    return invalid;
  }

  std::vector<double> ntscIntervals;
  std::vector<double> palIntervals;
  ntscIntervals.reserve(horizontalIndices.size());
  palIntervals.reserve(horizontalIndices.size());

  const double targetPalSamples = static_cast<double>(sampleRate_) / FH_PAL;
  const double targetNtscSamples = static_cast<double>(sampleRate_) / FH_NTSC;

  for (uint32_t idx = 1; idx < horizontalIndices.size(); ++idx) {
    const auto currentIndex = horizontalIndices[idx];
    const auto previousIndex = horizontalIndices[idx - 1];
    const auto gap = pulses[currentIndex].absCenterPosition - pulses[previousIndex].absCenterPosition;
    if (gap <= 0.0) {
      continue;
    }

    const double ntscRelError = std::abs(gap - targetNtscSamples) / targetNtscSamples;
    const double palRelError = std::abs(gap - targetPalSamples) / targetPalSamples;
    const double bestError = std::min(ntscRelError, palRelError);

    if (bestError > MAX_HORIZONTAL_REL_ERROR) {
      continue;
    }

    if (ntscRelError <= palRelError) {
      ntscIntervals.push_back(gap);
    } else {
      palIntervals.push_back(gap);
    }
  }

  auto buildEstimate = [&](const std::vector<double>& intervals, const VideoStandard standard, const double targetSamples) -> HorizontalEstimate {
    HorizontalEstimate estimate{};
    if (intervals.size() < MIN_HORIZONTAL_INTERVALS_FOR_ESTIMATE) {
      return estimate;
    }

    const double sum = std::accumulate(intervals.begin(), intervals.end(), 0.0);
    const double mean = sum / static_cast<double>(intervals.size());

    double varianceAccumulator = 0.0;
    for (const double interval : intervals) {
      const double diff = interval - mean;
      varianceAccumulator += diff * diff;
    }
    const double variance = varianceAccumulator / static_cast<double>(intervals.size());
    const double deviation = std::sqrt(variance);
    const double relativeStdDev = (mean > 0.0) ? (deviation / mean) : std::numeric_limits<double>::infinity();
    const double relativeError = std::abs(mean - targetSamples) / targetSamples;

    if (relativeStdDev > MAX_HORIZONTAL_REL_STD) {
      return estimate;
    }
    if (relativeError > MAX_HORIZONTAL_REL_ERROR) {
      return estimate;
    }

    estimate.valid = true;
    estimate.meanSamples = mean;
    estimate.deviationSamples = deviation;
    estimate.relativeError = relativeError;
    estimate.dominantStandard = standard;
    estimate.sampleCount = intervals.size();
    return estimate;
  };

  HorizontalEstimate best = buildEstimate(ntscIntervals, NTSC_STANDARD, targetNtscSamples);
  const HorizontalEstimate palCandidate = buildEstimate(palIntervals, PAL_STANDARD, targetPalSamples);

  auto better = [](const HorizontalEstimate& lhs, const HorizontalEstimate& rhs) -> bool {
    if (!lhs.valid) {
      return false;
    }
    if (!rhs.valid) {
      return true;
    }
    if (lhs.sampleCount != rhs.sampleCount) {
      return lhs.sampleCount > rhs.sampleCount;
    }
    if (std::abs(lhs.relativeError - rhs.relativeError) > 1e-6) {
      return lhs.relativeError < rhs.relativeError;
    }
    return lhs.deviationSamples < rhs.deviationSamples;
  };

  if (better(palCandidate, best)) {
    best = palCandidate;
  }

  return best;
}

std::vector<VideoStandardDetector::VerticalBurst> VideoStandardDetector::extractVerticalBursts(
  const std::vector<VideoSyncPulse>& pulses,
  const std::vector<SyncType>& types) const
{
  std::vector<VerticalBurst> bursts;
  const uint32_t count = pulses.size();

  uint32_t i = 0;
  while (i < count) {
    if (types[i] != SyncType::Vertical) {
      ++i;
      continue;
    }

    const uint32_t start = i;
    uint32_t end = i;
    while (
      end + 1 < count &&
      types[end + 1] == SyncType::Vertical &&
      signalRangeEstimator_.isHalfHorizontalLineDistance(pulses[end + 1].absCenterPosition - pulses[end].absCenterPosition)
    ) {
      ++end;
    }

    VerticalBurst burst{};
    burst.verticalCount = end - start + 1;

    uint32_t prevIndex = start;
    while (prevIndex > 0 && types[prevIndex - 1] == SyncType::Equalizing) {
      const double spacing = pulses[prevIndex].absCenterPosition - pulses[prevIndex - 1].absCenterPosition;
      if (!signalRangeEstimator_.isHalfHorizontalLineDistance(spacing)) {
        break;
      }
      ++burst.equalizingBefore;
      --prevIndex;
    }

    uint32_t nextIndex = end;
    while (nextIndex + 1 < count && types[nextIndex + 1] == SyncType::Equalizing) {
      const double spacing = pulses[nextIndex + 1].absCenterPosition - pulses[nextIndex].absCenterPosition;
      if (!signalRangeEstimator_.isHalfHorizontalLineDistance(spacing)) {
        break;
      }
      ++burst.equalizingAfter;
      ++nextIndex;
    }

    bursts.push_back(burst);
    i = end + 1;
  }

  return bursts;
}

VideoStandard VideoStandardDetector::inferStandard(
  const HorizontalEstimate& hEstimate,
  const std::vector<VerticalBurst>& bursts,
  const std::vector<SyncType>& types)
{
  int ntscScore = 0;
  int palScore = 0;
  uint32_t ntscMatches = 0;
  uint32_t palMatches = 0;

  if (hEstimate.valid) {
    if (hEstimate.dominantStandard == NTSC_STANDARD) {
      ntscScore += 6;
      if (hEstimate.relativeError <= STRICT_FREQ_TOLERANCE) {
        ntscScore += 2;
      }
    } else if (hEstimate.dominantStandard == PAL_STANDARD) {
      palScore += 6;
      if (hEstimate.relativeError <= STRICT_FREQ_TOLERANCE) {
        palScore += 2;
      }
    }
  }

  auto deviationForTarget = [](const VerticalBurst& burst, const int target) -> int {
    const int before = std::abs(static_cast<int>(burst.equalizingBefore) - target);
    const int mid = std::abs(static_cast<int>(burst.verticalCount) - target);
    const int after = std::abs(static_cast<int>(burst.equalizingAfter) - target);
    return before + mid + after;
  };

  for (const auto& burst : bursts) {
    const int devNTSC = deviationForTarget(burst, 6);
    const int devPAL = deviationForTarget(burst, 5);

    if (devNTSC <= devPAL && devNTSC <= 3) {
      ntscScore += 3;
      ++ntscMatches;
    } else if (devPAL < devNTSC && devPAL <= 3) {
      palScore += 3;
      ++palMatches;
    }
  }

  if (ntscMatches >= 2) {
    ntscScore += 2;
  }
  if (palMatches >= 2) {
    palScore += 2;
  }

  uint32_t bestHorizontalRun = 0;
  uint32_t currentRun = 0;
  for (const auto type : types) {
    if (type == SyncType::Horizontal) {
      ++currentRun;
    } else {
      bestHorizontalRun = std::max(bestHorizontalRun, currentRun);
      currentRun = 0;
    }
  }
  bestHorizontalRun = std::max(bestHorizontalRun, currentRun);

  if (bestHorizontalRun >= 220 && bestHorizontalRun <= 270) {
    ntscScore += 1;
  } else if (bestHorizontalRun >= 280 && bestHorizontalRun <= 340) {
    palScore += 1;
  }

  const int maxScore = std::max(ntscScore, palScore);
  const int minScore = std::min(ntscScore, palScore);

  if (maxScore < MIN_CONFIDENCE_SCORE) {
    return UNKNOWN_VIDEO_STANDARD;
  }
  if (maxScore - minScore < SCORE_MARGIN_REQUIRED) {
    return UNKNOWN_VIDEO_STANDARD;
  }

  return (ntscScore > palScore) ? NTSC_STANDARD : PAL_STANDARD;
}

}
