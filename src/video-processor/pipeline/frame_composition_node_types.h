#pragma once
#include "../video_standard.h"
#include <vector>

namespace IQVideoProcessor::Pipeline {

struct FrameCompositionResult {
  std::vector<uint8_t> data;  // JPEG compressed frame data
  uint32_t dataSize;            // Actual size of JPEG data in bytes
  VideoStandard videoStandard;
  uint32_t frameNumber{0};
  uint32_t width{0};
  uint32_t height{0};
};

} // namespace IQVideoProcessor::Pipeline
