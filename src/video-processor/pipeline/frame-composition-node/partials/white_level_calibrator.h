#pragma once

#include <cstddef>
#include <cstdint>
#include <vector>
#include "../../../../types.h"

namespace IQVideoProcessor::Pipeline {

struct WhiteLevelCalibratorSettings {
  float topFraction;
  std::size_t minTopSampleCount;
  VSampleFloat minDynamicRange;
  VSampleFloat initialWhiteLevel;
  float attackPercentPerSecond;
  float releasePercentPerSecond;
};

class WhiteLevelCalibrator {
public:
  explicit WhiteLevelCalibrator(const WhiteLevelCalibratorSettings& settings);

  void reset();
  void setNominalFrameRate(float frameRate);
  void setExpectedLineCount(std::size_t lineCount);

  void beginFrame(VSampleFloat blackReference);
  void accumulateLinePeak(const VSampleFloat* samples, uint32_t sampleCount);
  void finalizeFrame();

  VSampleFloat currentWhiteLevel() const;

private:
  float computeSmoothingAlpha(float percentPerSecond) const;
  VSampleFloat applyBounds(VSampleFloat candidate) const;

  const WhiteLevelCalibratorSettings settings_;
  float nominalFrameRate_{25.0f};
  VSampleFloat blackReference_{0.0f};
  VSampleFloat filteredWhiteLevel_;
  std::vector<VSampleFloat> linePeaks_;
};

} // namespace IQVideoProcessor::Pipeline
