#include "./white_level_calibrator.h"
#include <algorithm>
#include <cmath>

namespace IQVideoProcessor::Pipeline {

namespace {
constexpr VSampleFloat MAX_WHITE_LEVEL = static_cast<VSampleFloat>(M_PI);
constexpr float FALLBACK_FRAME_RATE = 25.0f;

inline std::size_t ceilToSizeT(float value) {
  if (value <= 0.0f) return 0;
  const auto truncated = static_cast<std::size_t>(value);
  return (value > static_cast<float>(truncated)) ? truncated + 1 : truncated;
}
} // namespace

WhiteLevelCalibrator::WhiteLevelCalibrator(const WhiteLevelCalibratorSettings& settings)
  : settings_(settings),
    filteredWhiteLevel_(settings.initialWhiteLevel) {}

void WhiteLevelCalibrator::reset() {
  linePeaks_.clear();
  blackReference_ = 0.0f;
  filteredWhiteLevel_ = settings_.initialWhiteLevel;
  nominalFrameRate_ = FALLBACK_FRAME_RATE;
}

void WhiteLevelCalibrator::setNominalFrameRate(const float frameRate) {
  nominalFrameRate_ = frameRate > 0.0f ? frameRate : FALLBACK_FRAME_RATE;
}

void WhiteLevelCalibrator::setExpectedLineCount(const std::size_t lineCount) {
  if (lineCount > linePeaks_.capacity()) {
    linePeaks_.reserve(lineCount);
  }
}

void WhiteLevelCalibrator::beginFrame(const VSampleFloat blackReference) {
  blackReference_ = blackReference;
  linePeaks_.clear();
  filteredWhiteLevel_ = applyBounds(filteredWhiteLevel_);
}

void WhiteLevelCalibrator::accumulateLinePeak(const VSampleFloat* samples, const uint32_t sampleCount) {
  if (samples == nullptr || sampleCount == 0U) return;

  VSampleFloat peak = blackReference_;
  for (uint32_t i = 0; i < sampleCount; ++i) {
    const VSampleFloat sample = samples[i];
    if (!std::isfinite(sample)) continue;
    if (sample > peak) {
      peak = sample;
    }
  }

  linePeaks_.push_back(peak);
}

void WhiteLevelCalibrator::finalizeFrame() {
  if (!linePeaks_.empty()) {
    auto removeIt = std::remove_if(
      linePeaks_.begin(),
      linePeaks_.end(),
      [](const VSampleFloat value) { return !std::isfinite(value); }
    );
    linePeaks_.erase(removeIt, linePeaks_.end());
  }

  VSampleFloat candidate = filteredWhiteLevel_;
  if (!linePeaks_.empty()) {
    std::sort(linePeaks_.begin(), linePeaks_.end());

    const std::size_t count = linePeaks_.size();
    const float scaledTop = settings_.topFraction * static_cast<float>(count);
    std::size_t topCount = ceilToSizeT(scaledTop);

    if (topCount < settings_.minTopSampleCount) {
      topCount = settings_.minTopSampleCount;
    }
    if (topCount == 0) {
      topCount = 1;
    }
    if (topCount > count) {
      topCount = count;
    }

    const std::size_t startIndex = count - topCount;
    float sum = 0.0f;
    for (std::size_t i = startIndex; i < count; ++i) {
      sum += linePeaks_[i];
    }
    const float average = sum / static_cast<float>(topCount);
    candidate = static_cast<VSampleFloat>(average);
  }

  candidate = applyBounds(candidate);
  const VSampleFloat delta = candidate - filteredWhiteLevel_;
  const float percent = delta >= 0.0f ? settings_.attackPercentPerSecond : settings_.releasePercentPerSecond;
  const float alpha = computeSmoothingAlpha(percent);
  filteredWhiteLevel_ = static_cast<VSampleFloat>(filteredWhiteLevel_ + alpha * delta);
  filteredWhiteLevel_ = applyBounds(filteredWhiteLevel_);

  linePeaks_.clear();
}

VSampleFloat WhiteLevelCalibrator::currentWhiteLevel() const {
  return applyBounds(filteredWhiteLevel_);
}

float WhiteLevelCalibrator::computeSmoothingAlpha(const float percentPerSecond) const {
  const float rate = std::max(percentPerSecond, 0.0f) * 0.01f;
  if (rate <= 0.0f) return 0.0f;

  const float frameRate = nominalFrameRate_ > 0.0f ? nominalFrameRate_ : FALLBACK_FRAME_RATE;
  const float frameInterval = 1.0f / frameRate;
  const float exponent = -rate * frameInterval;
  const float attenuation = std::expf(exponent);
  float alpha = 1.0f - attenuation;
  if (alpha < 0.0f) alpha = 0.0f;
  if (alpha > 1.0f) alpha = 1.0f;
  return alpha;
}

VSampleFloat WhiteLevelCalibrator::applyBounds(const VSampleFloat candidate) const {
  VSampleFloat bounded = std::isfinite(candidate) ? candidate : MAX_WHITE_LEVEL;
  const VSampleFloat minWhite = blackReference_ + settings_.minDynamicRange;

  if (bounded < minWhite) {
    bounded = minWhite;
  }
  if (bounded > MAX_WHITE_LEVEL) {
    bounded = MAX_WHITE_LEVEL;
  }
  return bounded;
}

} // namespace IQVideoProcessor::Pipeline
