#pragma once
#include "../../../../types.h"
#include <memory>
#include <string>
#include <optional>
#include <cmath>

namespace IQVideoProcessor::Pipeline {

class FrameCanvas {
public:
  FrameCanvas(uint32_t width, uint32_t height);
  ~FrameCanvas();
  void reset() const;
  void setVideoLineRawData(uint32_t lineIndex, const VSampleFloat *data, uint32_t dataSize) const;
  [[nodiscard]] uint32_t getMinRenderBufferSize() const;

  /**
   * Render the frame canvas to JPEG data in the provided buffer
   * @param outputBuffer Pre-allocated buffer for JPEG data
   * @param outputBufferSize Size of the output buffer
   * @param blackLevel Value to map to black (0)
   * @param whiteLevel Value to map to white (255), defaults to π
   * @return Size of rendered JPEG data in bytes, or std::nullopt if rendering failed
   */
  std::optional<uint32_t> render(uint8_t* outputBuffer, uint32_t outputBufferSize, VSampleFloat blackLevel, VSampleFloat whiteLevel = M_PI);

private:
  uint32_t width_;
  uint32_t height_;
  uint32_t totalPixels_;
  uint32_t minRenderBufferSize_;
  std::unique_ptr<VSampleFloat[]> rawBuffer_;
  std::unique_ptr<uint8_t[]> grayscaleBuffer_;

  // TurboJPEG compressor handle (initialized once)
  void *tjHandle_;

  const uint8_t* convertToGrayscale(VSampleFloat blackLevel, VSampleFloat whiteLevel);
  uint32_t exportGrayscaleToJpeg(uint8_t* outputBuffer, uint32_t outputBufferSize, const uint8_t* imageData, int quality = 100) const;
};

}
