#include "./frame_composition_node.h"
#include "logging/logging.h"
#include <cmath>
#include <iostream>
#include <sstream>
#include "../../video_processor_configs.h"

namespace IQVideoProcessor::Pipeline {

namespace {
constexpr float FALLBACK_FRAME_RATE = 25.0f;
} // namespace

FrameCompositionNode::FrameCompositionNode(const SampleRate sampleRate)
  : sampleRate_(sampleRate),
    whiteLevelCalibrator_(getWhiteLevelCalibratorDefaults()) {
  setRunning();
}

FrameCompositionNode::~FrameCompositionNode() {
  PipelineComponent::stop();
}

bool FrameCompositionNode::process(LineDetectionEvent& event) {
  if (!running()) return false;

  switch (event.type) {
    case LineDetectionEventType::STANDARD_DETECTED:
      handleStandardDetected(event.videoStandard);
      break;
    case LineDetectionEventType::SYNC_LOCKED:
      handleSyncLock();
      break;
    case LineDetectionEventType::SYNC_LOCK_LOST:
      handleSyncLockLost();
      break;
    case LineDetectionEventType::FRAME_FIELD_BEGIN:
      handleFrameFieldBegin(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::FRAME_FIELD_END:
      handleFrameFieldEnd(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::LINE_RECEIVED:
      handleLineReceived(event.data, event.dataSize, event.lineNumber, event.isTopField);
      break;
    case LineDetectionEventType::EQUALIZATION:
      handleEqualization(event.data, event.dataSize);
      break;
    case LineDetectionEventType::UNKNOWN:
    default:
      handleUnknownEvent();
      break;
  }

  return running();
}

void FrameCompositionNode::handleStandardDetected(const VideoStandard standard) {
  if (standard == UNKNOWN_VIDEO_STANDARD) {
    isStandardDetected_ = false;
    currentStandard_ = UNKNOWN_VIDEO_STANDARD;
    frameCanvas_.reset();
    whiteLevelCalibrator_.reset();
    currentCompositionResult_.data.clear();
    currentCompositionResult_.dataSize = 0;
    LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown video standard received");
    return;
  }

  if (currentStandard_ == standard && frameCanvas_) {
    return;
  }

  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "switching video standard from " << currentStandard_ << " to " << standard);

  auto [width, height] = getVideoStandardDimensions(standard);
  frameCanvas_ = std::make_unique<FrameCanvas>(width, height);
  currentStandard_ = standard;
  isInterlaced_ = isVideoStandardInterlaced(standard);
  isTFF_ = isVideoStandardTFF(standard);

  auto [leftPaddingSec, rightPaddingSec] = getVideoStandardPaddings(standard);
  lineLeftPadding_ = static_cast<uint32_t>(std::ceil(leftPaddingSec * sampleRate_));
  lineRightPadding_ = static_cast<uint32_t>(std::ceil(rightPaddingSec * sampleRate_));
  lineHorizontalPadding_ = lineLeftPadding_ + lineRightPadding_;

  currentCompositionResult_.data.resize(frameCanvas_->getMinRenderBufferSize());
  currentCompositionResult_.width = width;
  currentCompositionResult_.height = height;
  currentCompositionResult_.videoStandard = standard;
  currentCompositionResult_.frameNumber = 0;
  currentCompositionResult_.dataSize = 0;

  equalizationBlackLevelCalcRegionSamples_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_REGION_SEC * sampleRate_));
  equalizationBlackLevelCalcRegionOffset_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_OFFSET_SEC * sampleRate_));
  equalizationBlackLevel_ = 0.0f;

  nominalFrameRate_ = IQVideoProcessor::Pipeline::getVideoStandardFrameRate(standard);
  if (nominalFrameRate_ <= 0.0f) {
    nominalFrameRate_ = FALLBACK_FRAME_RATE;
  }

  whiteLevelCalibrator_.reset();
  whiteLevelCalibrator_.setExpectedLineCount(static_cast<std::size_t>(height));
  whiteLevelCalibrator_.setNominalFrameRate(nominalFrameRate_);
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);

  isStandardDetected_ = true;
  processInitialFrame();
}

void FrameCompositionNode::handleSyncLock() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock acquired");
  isSyncLocked_ = true;
}

void FrameCompositionNode::handleSyncLockLost() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock lost");
  isSyncLocked_ = false;
  processCompleteFrame();
}

void FrameCompositionNode::handleFrameFieldBegin(const uint32_t frameFieldNumber, const bool isTopField) {
  if (!isStandardDetected_ || !isSyncLocked_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "warning - frame field begin received but standard not detected or sync not locked");
    return;
  }

  if (!currentProcessingFrame_.has_value()) {
    if (!isTopField) {
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "waiting for top field to start frame processing...");
      isSyncingWithTopField_ = true;
      return;
    }
    isSyncingWithTopField_ = false;
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
    resetFrameStatistics();
    return;
  }

  if (isTopField || !isInterlaced_) {
    isSyncingWithTopField_ = false;
    processCompleteFrame();
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
    resetFrameStatistics();
    LOG_VERBOSE(FRAME_COMPOSITION_NODE, "begin processing next top field in series!");
  } else {
    auto& cfi = currentProcessingFrame_.value();
    cfi.hasBottomFieldPart = true;
  }
}

void FrameCompositionNode::handleFrameFieldEnd(const uint32_t /*frameFieldNumber*/, const bool /*isTopField*/) {
  if (!currentProcessingFrame_.has_value()) return;

  const auto& cfi = currentProcessingFrame_.value();
  if ((cfi.hasTopFieldPart && cfi.hasBottomFieldPart) || (!isInterlaced_ && cfi.hasTopFieldPart)) {
    processCompleteFrame();
  }
}

void FrameCompositionNode::handleLineReceived(
  const std::vector<VSampleFloat>& data,
  const uint32_t dataSize,
  const uint32_t lineNumber,
  const bool isTopField
) {
  if (!isStandardDetected_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but standard is not detected");
    return;
  }
  if (!currentProcessingFrame_.has_value()) {
    if (!isSyncingWithTopField_) {
      LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but no frame field is active");
    }
    return;
  }

  const uint32_t croppedDataStart = lineLeftPadding_;
  const uint32_t croppedDataSize = dataSize > lineHorizontalPadding_ ? dataSize - lineHorizontalPadding_ : 0U;
  if (croppedDataSize == 0U) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but padding exceeds data size");
    return;
  }

  const VSampleFloat* croppedData = &data[croppedDataStart];
  if (isInterlaced_) {
    const uint32_t lineIndex = (lineNumber << 1) + (isTFF_ == isTopField ? 1U : 0U);
    frameCanvas_->setVideoLineRawData(lineIndex, croppedData, croppedDataSize);
  } else {
    frameCanvas_->setVideoLineRawData(lineNumber, croppedData, croppedDataSize);
  }

  whiteLevelCalibrator_.accumulateLinePeak(croppedData, croppedDataSize);
}

void FrameCompositionNode::handleEqualization(const std::vector<VSampleFloat>& data, const uint32_t dataSize) {
  if (equalizationBlackLevelCalcRegionOffset_ + equalizationBlackLevelCalcRegionSamples_ > dataSize) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "equalization data received but not enough samples to calculate black level");
    equalizationBlackLevel_ = data[dataSize >> 1];
    return;
  }

  float accumulator = 0.0f;
  const VSampleFloat* blackRegionData = &data[equalizationBlackLevelCalcRegionOffset_];
  for (uint32_t i = 0; i < equalizationBlackLevelCalcRegionSamples_; ++i) {
    accumulator += blackRegionData[i];
  }
  equalizationBlackLevel_ = static_cast<VSampleFloat>(
    accumulator / static_cast<float>(equalizationBlackLevelCalcRegionSamples_)
  );
}

void FrameCompositionNode::handleUnknownEvent() {
  LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown event received");
}

void FrameCompositionNode::processCompleteFrame() {
  if (!currentProcessingFrame_.has_value()) return;

  whiteLevelCalibrator_.finalizeFrame();
  renderAndSendNextFrame();
  currentProcessingFrame_.reset();
}

void FrameCompositionNode::renderAndSendNextFrame() {
  if (!frameCanvas_) return;

  const VSampleFloat whiteLevel = whiteLevelCalibrator_.currentWhiteLevel();
  const auto renderResult = frameCanvas_->render(
    currentCompositionResult_.data.data(),
    currentCompositionResult_.data.size(),
    equalizationBlackLevel_,
    whiteLevel
  );

  if (renderResult.has_value()) {
    currentCompositionResult_.dataSize = renderResult.value();
    currentCompositionResult_.frameNumber = frameCounter_++;

    if (!sendOutput(currentCompositionResult_)) {
      stop();
    }
  } else {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "failed to render the frame");
    stop();
  }
}

void FrameCompositionNode::processInitialFrame() {
  if (frameCanvas_) {
    frameCanvas_->reset();
  }
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);
  whiteLevelCalibrator_.finalizeFrame();
  renderAndSendNextFrame();
}

void FrameCompositionNode::resetFrameStatistics() {
  whiteLevelCalibrator_.beginFrame(equalizationBlackLevel_);
}

} // namespace IQVideoProcessor::Pipeline
