#include "./frame_composition_node.h"
#include "logging/logging.h"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <sstream>
#include "../../video_processor_configs.h"

namespace IQVideoProcessor::Pipeline {

constexpr double DEFAULT_FRAME_RATE = 25.0;
constexpr double WHITE_LEVEL_ATTACK_TIME_SEC = 0.35;
constexpr double WHITE_LEVEL_RELEASE_TIME_SEC = 0.85;
constexpr double WHITE_LEVEL_TOP_FRACTION = 0.12;
constexpr VSampleFloat WHITE_LEVEL_MIN_DYNAMIC_RANGE = 0.12f;
constexpr VSampleFloat WHITE_LEVEL_INITIAL_VALUE = 1.0f;
constexpr uint32_t WHITE_LEVEL_MIN_TOP_SAMPLE_COUNT = 12;

inline double computeSmoothingAlpha(const double frameRate, const double timeConstant) {
  if (timeConstant <= 0.0 || frameRate <= 0.0) return 1.0;
  const double frameInterval = 1.0 / frameRate;
  const double alpha = 1.0 - std::exp(-frameInterval / timeConstant);
  return std::clamp(alpha, 0.0, 1.0);
}

FrameCompositionNode::FrameCompositionNode(const SampleRate sampleRate): sampleRate_(sampleRate) {
  setRunning();
}

FrameCompositionNode::~FrameCompositionNode() {
  PipelineComponent::stop();
}

bool FrameCompositionNode::process(LineDetectionEvent& event) {
  if (!running()) return false;

  // Route events to appropriate handlers based on event type
  switch (event.type) {
    case LineDetectionEventType::STANDARD_DETECTED:
      handleStandardDetected(event.videoStandard);
      break;
    case LineDetectionEventType::SYNC_LOCKED:
      handleSyncLock();
      break;
    case LineDetectionEventType::SYNC_LOCK_LOST:
      handleSyncLockLost();
      break;
    case LineDetectionEventType::FRAME_FIELD_BEGIN:
      handleFrameFieldBegin(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::FRAME_FIELD_END:
      handleFrameFieldEnd(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::LINE_RECEIVED:
      handleLineReceived(event.data, event.dataSize, event.lineNumber, event.isTopField);
      break;
    case LineDetectionEventType::EQUALIZATION:
      handleEqualization(event.data, event.dataSize);
      break;
    case LineDetectionEventType::UNKNOWN:
    default:
      handleUnknownEvent();
      break;
  }

  return running();
}

void FrameCompositionNode::handleStandardDetected(const VideoStandard standard) {
  if (standard == UNKNOWN_VIDEO_STANDARD) {
    isStandardDetected_ = false;
    currentStandard_ = UNKNOWN_VIDEO_STANDARD;
    frameCanvas_.reset();
    resetFrameStatistics();
    filteredWhiteLevel_ = WHITE_LEVEL_INITIAL_VALUE;
    nominalFrameRate_ = 0.0;
    LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown video standard received");
    return;
  }

  if (currentStandard_ == standard) return; // No change
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "switching video standard from " << currentStandard_ << " to " << standard);

  // Prepare the frame canvas for the new standard, allocating necessary resources
  auto [width, height] = getVideoStandardDimensions(standard);
  frameCanvas_.reset();
  frameCanvas_ = std::make_unique<FrameCanvas>(width, height);
  currentStandard_ = standard;
  isInterlaced_ = isVideoStandardInterlaced(standard);
  isTFF_ = isVideoStandardTFF(standard);
  auto [leftPaddingSec, rightPaddingSec] = getVideoStandardPaddings(standard);
  lineLeftPadding_ = static_cast<uint32_t>(std::ceil(leftPaddingSec * sampleRate_));
  lineRightPadding_ = static_cast<uint32_t>(std::ceil(rightPaddingSec * sampleRate_));
  lineHorizontalPadding_ = lineLeftPadding_ + lineRightPadding_;

  // Prepare the composed frame structure, reserving space for rendered data
  currentCompositionResult_.data.resize(frameCanvas_->getMinRenderBufferSize());
  currentCompositionResult_.width = width;
  currentCompositionResult_.height = height;
  currentCompositionResult_.videoStandard = standard;
  currentCompositionResult_.frameNumber = 0;
  currentCompositionResult_.dataSize = 0;

  equalizationBlackLevelCalcRegionSamples_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_REGION_SEC * sampleRate_));
  equalizationBlackLevelCalcRegionOffset_ = static_cast<uint32_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_OFFSET_SEC * sampleRate_));

  equalizationBlackLevel_ = 0.0f;
  frameLinePeaks_.clear();
  frameLinePeaks_.reserve(height);
  nominalFrameRate_ = getVideoStandardFrameRate(standard);
  if (nominalFrameRate_ <= 0.0) {
    nominalFrameRate_ = DEFAULT_FRAME_RATE;
  }
  filteredWhiteLevel_ = applyWhiteLevelBounds(WHITE_LEVEL_INITIAL_VALUE);
  isStandardDetected_ = true;

  processInitialFrame();
}

void FrameCompositionNode::handleSyncLock() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock acquired");
  isSyncLocked_ = true;
}

void FrameCompositionNode::handleSyncLockLost() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock lost");
  isSyncLocked_ = false;
  // Processing the frame despite incomplete state
  processCompleteFrame();
}

void FrameCompositionNode::handleFrameFieldBegin(const uint32_t frameFieldNumber, const bool isTopField) {
  if (!isStandardDetected_ || !isSyncLocked_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "warning - frame field begin received but standard not detected or sync not locked");
    return;
  }

  if (!currentProcessingFrame_.has_value()) {
    if (!isTopField) {
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "waiting for top field to start frame processing...");
      isSyncingWithTopField_ = true;
      return; // We expect the first part to be top field in any case, so ignore bottom field part if no frame is active
    }
    isSyncingWithTopField_ = false;
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
    resetFrameStatistics();
  } else {
    if (isTopField || !isInterlaced_) {
      isSyncingWithTopField_ = false;
      // We can't have the second top field part or normally reach here in interlaced mode
      processCompleteFrame(); // So we end the previous frame first, and begin a new one starting from top field part
      resetFrameStatistics();
      currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "begin processing next top field in series!");
      return;
    }
    // We are receiving the bottom field part of the current frame
    auto &cfi = currentProcessingFrame_.value();
    cfi.hasBottomFieldPart = true;
    // std::cout << "FrameCompositionNode: Frame " << frameFieldNumber << " begin (BOTTOM field)" << std::endl;
  }
}

void FrameCompositionNode::handleFrameFieldEnd(const uint32_t frameFieldNumber, const bool isTopField) {
  // We don't care about the detected standard or sync state here, just validate frame field info
  if (!currentProcessingFrame_.has_value()) return;

  const auto &cfi = currentProcessingFrame_.value(); // Copy current frame field info
  // Ensure we have both fields for interlaced mode, or at least top field for regular mode
  if ((cfi.hasTopFieldPart && cfi.hasBottomFieldPart) || (!isInterlaced_ && cfi.hasTopFieldPart)) {
    processCompleteFrame(); // Frame is complete, lets process it
  }
}

void FrameCompositionNode::handleLineReceived(
  const std::vector<VSampleFloat> &data,
  const uint32_t dataSize,
  const uint32_t lineNumber,
  const bool isTopField
) {
  // Sanity checks
  if (!isStandardDetected_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but standard is not detected");
    return;
  }
  if (!currentProcessingFrame_.has_value()) {
    if (!isSyncingWithTopField_) {
      LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but no frame field is active");
    }
    return;
  }

  const auto croppedDataStart = lineLeftPadding_;
  const auto croppedDataSize = dataSize > lineHorizontalPadding_ ? dataSize - lineHorizontalPadding_ : 0;
  if (croppedDataSize == 0) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but padding exceeds data size");
    return;
  }

  const auto* croppedData = &data[croppedDataStart];
  if (isInterlaced_) {
    const auto lineIndex = (lineNumber << 1) + (isTFF_ == isTopField ? 1 : 0);
    frameCanvas_->setVideoLineRawData(lineIndex, croppedData, croppedDataSize);
  } else {
    frameCanvas_->setVideoLineRawData(lineNumber, croppedData, croppedDataSize);
  }

  accumulateLinePeak(croppedData, croppedDataSize);
  // std::cout << "FrameCompositionNode: Line " << lineNumber << " received ("<< (isTopField ? "TOP FIELD" : "BOTTOM FIELD") << " field), " << dataSize << " samples" << std::endl;
}

void FrameCompositionNode::handleEqualization(const std::vector<VSampleFloat>& data, const uint32_t dataSize) {
  // TODO Calculate the average level and accumulate over multiple equalization pulses for better accuracy in more safe manner
  if (equalizationBlackLevelCalcRegionOffset_ + equalizationBlackLevelCalcRegionSamples_ > dataSize) {
    // We don't have enough data to calculate the black level
    LOG_ERROR(FRAME_COMPOSITION_NODE, "equalization data received but not enough samples to calculate black level");
    equalizationBlackLevel_ = data[dataSize >> 1]; // Take the middle sample as black level
    filteredWhiteLevel_ = applyWhiteLevelBounds(filteredWhiteLevel_);
    return;
  }
  // Calculate average black level from the defined region
  double accumulator = 0.0f;
  const auto* blackRegionData = &data[equalizationBlackLevelCalcRegionOffset_];
  for (uint32_t i = 0; i < equalizationBlackLevelCalcRegionSamples_; i++) {
    accumulator += blackRegionData[i];
  }
  equalizationBlackLevel_ = static_cast<VSampleFloat>(accumulator / static_cast<double>(equalizationBlackLevelCalcRegionSamples_));
  filteredWhiteLevel_ = applyWhiteLevelBounds(filteredWhiteLevel_);
}

void FrameCompositionNode::handleUnknownEvent() {
  LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown event received");
}

void FrameCompositionNode::processCompleteFrame() {
  if (!currentProcessingFrame_.has_value()) return; // No frame to process

  const auto frameWhiteCandidate = calculateFrameWhiteLevelCandidate();
  updateWhiteLevel(frameWhiteCandidate);

  renderAndSendNextFrame();
  currentProcessingFrame_.reset(); // Clear current frame field info
  resetFrameStatistics();
}

void FrameCompositionNode::renderAndSendNextFrame() {
  if (!frameCanvas_) return;

  const auto boundedWhiteLevel = applyWhiteLevelBounds(filteredWhiteLevel_);
  const auto renderResult = frameCanvas_->render(
    currentCompositionResult_.data.data(),
    currentCompositionResult_.data.size(),
    equalizationBlackLevel_,
    boundedWhiteLevel
  );

  if (renderResult.has_value()) {
    currentCompositionResult_.dataSize = renderResult.value();
    currentCompositionResult_.frameNumber = frameCounter_++;

    if (!sendOutput(currentCompositionResult_)) {
      stop();
    }
  } else {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "failed to render the frame");
    stop();
  }
}

void FrameCompositionNode::processInitialFrame() {
  resetFrameStatistics();
  if (frameCanvas_) {
    frameCanvas_->reset();
  }
  filteredWhiteLevel_ = applyWhiteLevelBounds(filteredWhiteLevel_);
  renderAndSendNextFrame();
}

void FrameCompositionNode::resetFrameStatistics() {
  frameLinePeaks_.clear();
}

void FrameCompositionNode::accumulateLinePeak(const VSampleFloat* data, const uint32_t sampleCount) {
  if (data == nullptr || sampleCount == 0) return;

  VSampleFloat linePeak = equalizationBlackLevel_;
  for (uint32_t i = 0; i < sampleCount; ++i) {
    const auto sample = data[i];
    if (!std::isfinite(sample)) continue;
    if (sample > linePeak) {
      linePeak = sample;
    }
  }
  frameLinePeaks_.push_back(linePeak);
}

VSampleFloat FrameCompositionNode::calculateFrameWhiteLevelCandidate() {
  if (frameLinePeaks_.empty()) {
    return applyWhiteLevelBounds(filteredWhiteLevel_);
  }

  auto& peaks = frameLinePeaks_;
  peaks.erase(
    std::remove_if(peaks.begin(), peaks.end(), [](const VSampleFloat value) {
      return !std::isfinite(value);
    }),
    peaks.end()
  );

  if (peaks.empty()) {
    return applyWhiteLevelBounds(filteredWhiteLevel_);
  }

  std::sort(peaks.begin(), peaks.end());

  const auto count = peaks.size();
  auto topCount = static_cast<uint32_t>(std::ceil(static_cast<double>(count) * WHITE_LEVEL_TOP_FRACTION));
  if (topCount < WHITE_LEVEL_MIN_TOP_SAMPLE_COUNT) {
    topCount = std::min<uint32_t>(WHITE_LEVEL_MIN_TOP_SAMPLE_COUNT, count);
  }
  const auto startIndex = count > topCount ? count - topCount : 0;

  VSampleFloat accumulator = 0.0;
  uint32_t samples = 0;
  for (auto i = startIndex; i < count; ++i) {
    accumulator += peaks[i];
    ++samples;
  }

  if (samples == 0) {
    return applyWhiteLevelBounds(filteredWhiteLevel_);
  }

  const auto average = accumulator / static_cast<VSampleFloat>(samples);
  return applyWhiteLevelBounds(average);
}

void FrameCompositionNode::updateWhiteLevel(const VSampleFloat frameCandidate) {
  if (!std::isfinite(frameCandidate)) return;

  const auto safeCandidate = applyWhiteLevelBounds(frameCandidate);
  const double frameRate = nominalFrameRate_ > 0.0 ? nominalFrameRate_ : DEFAULT_FRAME_RATE;
  const double alpha = safeCandidate > filteredWhiteLevel_
    ? computeSmoothingAlpha(frameRate, WHITE_LEVEL_ATTACK_TIME_SEC)
    : computeSmoothingAlpha(frameRate, WHITE_LEVEL_RELEASE_TIME_SEC);
  const double effectiveAlpha = std::clamp(alpha, 0.0, 1.0);

  const double updated = (1.0 - effectiveAlpha) * static_cast<double>(filteredWhiteLevel_) + effectiveAlpha * static_cast<double>(safeCandidate);
  filteredWhiteLevel_ = applyWhiteLevelBounds(static_cast<VSampleFloat>(updated));
}

VSampleFloat FrameCompositionNode::applyWhiteLevelBounds(const VSampleFloat value) const {
  constexpr VSampleFloat maxWhite = M_PI;
  const VSampleFloat minWhite = equalizationBlackLevel_ + WHITE_LEVEL_MIN_DYNAMIC_RANGE;

  if (minWhite >= maxWhite) {
    return maxWhite;
  }

  VSampleFloat clampedValue = value;
  if (!std::isfinite(clampedValue)) {
    clampedValue = maxWhite;
  }

  return std::clamp(clampedValue, minWhite, maxWhite);
}

}
