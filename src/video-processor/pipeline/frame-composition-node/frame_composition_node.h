#pragma once
#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../line_detection_node_types.h"
#include "../frame_composition_node_types.h"
#include "./partials/frame_canvas.h"
#include "./partials/white_level_calibrator.h"
#include <memory>
#include <optional>

namespace IQVideoProcessor::Pipeline {

class FrameCompositionNode final : public SPipeline::StreamNode<LineDetectionEvent, FrameCompositionResult> {
public:
  explicit FrameCompositionNode(SampleRate sampleRate);
  ~FrameCompositionNode() override;

private:
  bool process(LineDetectionEvent& event) override;

  // Event handler methods
  void handleStandardDetected(VideoStandard standard);
  void handleSyncLock();
  void handleSyncLockLost();
  void handleFrameFieldBegin(uint32_t frameFieldNumber, bool isTopField);
  void handleFrameFieldEnd(uint32_t frameFieldNumber, bool isTopField);
  void handleLineReceived(const std::vector<VSampleFloat>& data, uint32_t dataSize, uint32_t lineNumber, bool isTopField);
  void handleEqualization(const std::vector<VSampleFloat>& data, uint32_t dataSize);
  void handleUnknownEvent();

  void processCompleteFrame();
  void renderAndSendNextFrame();
  void processInitialFrame();
  void resetFrameStatistics();

  SampleRate sampleRate_;
  VideoStandard currentStandard_{UNKNOWN_VIDEO_STANDARD};
  std::unique_ptr<FrameCanvas> frameCanvas_{nullptr};

  // State management
  bool isStandardDetected_{false};
  bool isInterlaced_{false};
  bool isTFF_{true};
  bool isSyncLocked_{false};
  bool isSyncingWithTopField_{false};

  struct FrameInfo {
    uint32_t initialFrameNumber;
    bool hasTopFieldPart;
    bool hasBottomFieldPart;
  };
  std::optional<FrameInfo> currentProcessingFrame_{std::nullopt};
  uint32_t frameCounter_{0};

  uint32_t lineLeftPadding_{0};
  uint32_t lineRightPadding_{0};
  uint32_t lineHorizontalPadding_{0};

  uint32_t equalizationBlackLevelCalcRegionSamples_{0};
  uint32_t equalizationBlackLevelCalcRegionOffset_{0};
  VSampleFloat equalizationBlackLevel_{0.0f};

  WhiteLevelCalibrator whiteLevelCalibrator_;
  float nominalFrameRate_{0.0f};

  FrameCompositionResult currentCompositionResult_;
};

} // namespace IQVideoProcessor::Pipeline