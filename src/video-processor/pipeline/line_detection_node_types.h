#pragma once
#include "../../types.h"
#include "../video_standard.h"
#include <vector>

namespace IQVideoProcessor::Pipeline {

enum class LineDetectionEventType {
  STANDARD_DETECTED,
  SYNC_LOCKED,
  SYNC_LOCK_LOST,
  FRAME_FIELD_BEGIN,
  FRAME_FIELD_END,
  EQUALIZATION,
  LINE_RECEIVED,
  UNKNOWN
};

struct LineDetectionEvent {
  LineDetectionEventType type{LineDetectionEventType::UNKNOWN};
  VideoStandard videoStandard{UNKNOWN_VIDEO_STANDARD};
  std::vector<VSampleFloat> data;
  uint32_t dataSize{0};
  uint32_t lineNumber{0};
  uint32_t frameFieldNumber{0};
  bool isTopField{false};
};

} // namespace IQVideoProcessor::Pipeline

