#include "./video_processor_configs.h"
#include "./pipeline/frame-composition-node/partials/white_level_calibrator.h"
#include <cmath>

namespace IQVideoProcessor::Pipeline {

namespace {
constexpr float DEFAULT_ATTACK_PERCENT_PER_SECOND = 120.0f;
constexpr float DEFAULT_RELEASE_PERCENT_PER_SECOND = 45.0f;
constexpr float DEFAULT_TOP_FRACTION = 0.12f;
constexpr std::size_t DEFAULT_MIN_TOP_SAMPLE_COUNT = 12;
constexpr VSampleFloat DEFAULT_MIN_DYNAMIC_RANGE = 0.12f;
constexpr VSampleFloat DEFAULT_INITIAL_WHITE_LEVEL = static_cast<VSampleFloat>(M_PI);
} // namespace

const WhiteLevelCalibratorSettings& getWhiteLevelCalibratorDefaults() {
  static const WhiteLevelCalibratorSettings SETTINGS{
    DEFAULT_TOP_FRACTION,
    DEFAULT_MIN_TOP_SAMPLE_COUNT,
    DEFAULT_MIN_DYNAMIC_RANGE,
    DEFAULT_INITIAL_WHITE_LEVEL,
    DEFAULT_ATTACK_PERCENT_PER_SECOND,
    DEFAULT_RELEASE_PERCENT_PER_SECOND
  };
  return SETTINGS;
}

float getVideoStandardFrameRate(IQVideoProcessor::VideoStandard standard) {
  return static_cast<float>(IQVideoProcessor::getVideoStandardFrameRate(standard));
}

} // namespace IQVideoProcessor::Pipeline
