#include "./video_processor.h"
#include "./video_processor_configs.h"
#include "logging/logging.h"
#include <algorithm>

namespace IQVideoProcessor {

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream, std::function<void()> onFrameAvailable, std::function<void(StopCode)> onStopped)
  : stream_(std::move(stream)),
    onFrameAvailable_(std::move(onFrameAvailable)),
    onStopped_(std::move(onStopped)) {
  LOG_VERBOSE(VIDEO_PROCESSOR, "creating");
}

VideoProcessor::~VideoProcessor() {
  LOG_VERBOSE(VIDEO_PROCESSOR, "destroying");
  // Ensure teardown; do not call back into JS synchronously here.
  stop(StopCode::ERROR);
  if (joinerThread_ && joinerThread_->joinable()) {
    joinerThread_->join();
  }
}

bool VideoProcessor::start() {
  LOG_VERBOSE(VIDEO_PROCESSOR, "starting");
  if (running_.load(std::memory_order_acquire)) return false;

  // Initialize the pipeline components
  if (!initialize()) return false;

  LOG_VERBOSE(VIDEO_PROCESSOR, "creating threads");
  // Create the main pipeline processing thread (demodulation->line detection->frame composition->output queue)
  pipelineMainThread_ = std::make_unique<std::thread>([this]() {
    LOG_VERBOSE(VIDEO_PROCESSOR, "pipeline thread started");
    pipelineThreadRunning_.store(true, std::memory_order_release);
    {
      std::unique_lock lock(threadsMutex_);
      threadsCv_.notify_one(); // wake acquisition thread
    }
    while (running_.load(std::memory_order_acquire)) {
      if (!acquisitionBridge_ || !acquisitionBridge_->tick()) {
        setStopCodeIfUnset(StopCode::PIPELINE_EXIT);
        break;
      }
    }
    // Ensure a stop request propagates
    const auto code = stopCode_.load(std::memory_order_acquire);
    stop(code == -1 ? StopCode::PIPELINE_EXIT : toStopCode(code));
  });

  acquisitionThread_ = std::make_unique<std::thread>([this]() {
    LOG_VERBOSE(VIDEO_PROCESSOR, "acquisition thread started");
    {
      std::unique_lock lock(threadsMutex_);
      threadsCv_.wait(lock, [this] {
        return pipelineThreadRunning_.load(std::memory_order_acquire);
      });
    }
    while (running_.load(std::memory_order_acquire)) {
      if (!acquisitionNode_ || !acquisitionNode_->tick()) {
        setStopCodeIfUnset(StopCode::PIPELINE_EXIT);
        break;
      }
    }
    const auto code = stopCode_.load(std::memory_order_acquire);
    stop(code == -1 ? StopCode::PIPELINE_EXIT : toStopCode(code));
  });

  if (!pipelineMainThread_ || !acquisitionThread_) {
    return false;
  }

  running_.store(true, std::memory_order_release);
  LOG_VERBOSE(VIDEO_PROCESSOR, "started");
  return true;
}

void VideoProcessor::stop(const StopCode reason) {
  setStopCodeIfUnset(reason);

  const auto wasRunning = running_.exchange(false, std::memory_order_acq_rel);
  LOG_VERBOSE(VIDEO_PROCESSOR, "requestStop; wasRunning=" << (wasRunning ? "true" : "false"));

  sendStopToPipeline();
  beginJoinAndTeardownIfNeeded();
}

void VideoProcessor::setStopCodeIfUnset(const StopCode code) {
  int expected = -1;
  const int v = stopCodeToInt(code);
  stopCode_.compare_exchange_strong(expected, v, std::memory_order_acq_rel);
}

void VideoProcessor::beginJoinAndTeardownIfNeeded() {
  if (bool expected = false; !joinerStarted_.compare_exchange_strong(expected, true, std::memory_order_acq_rel)) {
    return; // already started
  }

  joinerThread_ = std::make_unique<std::thread>([this]() {
    LOG_VERBOSE(VIDEO_PROCESSOR, "joiner: joining threads");

    if (acquisitionThread_ && acquisitionThread_->joinable()) acquisitionThread_->join();
    if (pipelineMainThread_ && pipelineMainThread_->joinable()) pipelineMainThread_->join();

    LOG_VERBOSE(VIDEO_PROCESSOR, "joiner: joined threads, deinitializing");
    deinitialize();

    auto code = stopCode_.load(std::memory_order_acquire);
    if (code == -1) code = stopCodeToInt(StopCode::ERROR);

    if (onStopped_) {
      try {
        onStopped_(toStopCode(code));
      } catch (...) {
        // swallow
      }
    }

    LOG_VERBOSE(VIDEO_PROCESSOR, "joiner: done");
  });
}

bool VideoProcessor::hasNextFrame() const {
  return outputQueueNode_ && outputQueueNode_->hasNextFrame();
}

Pipeline::FrameCompositionResult& VideoProcessor::getNextFrame() const {
  return outputQueueNode_->getNextFrame();
}

int32_t VideoProcessor::lastStopCode() const {
  return stopCode_.load(std::memory_order_acquire);
}

bool VideoProcessor::initialize() {
  LOG_VERBOSE(VIDEO_PROCESSOR, "initializing");
  if (!stream_) return false;

  sampleRate_ = stream_->sampleRate();
  if (sampleRate_ == 0) return false;

  // Calculating the maximum samples per video line, required to pre-allocate buffers and other computations
  const auto maxVideoLineSamples = static_cast<uint32_t>(static_cast<double>(sampleRate_) / MIN_LINE_RATE_HZ);
  if (maxVideoLineSamples < MIN_SAMPLES_PER_VIDEO_LINE) return false;

  // Calculate the desired amount of processable samples and overlap per window
  auto effectiveSamples = static_cast<uint32_t>(static_cast<double>(maxVideoLineSamples) * LINES_PER_CHUNK); // ~100 lines
  auto leftOverlap = maxVideoLineSamples * 8; // 8 lines overlap on each side, to ensure we catch non-standard VSync pulses

  // Create pipeline components using pointer-based storage
  acquisitionNode_ = std::make_unique<Pipeline::IQAcquisitionNode>(std::move(stream_), IQ_STREAM_READ_SIZE, effectiveSamples, leftOverlap);
  acquisitionBridge_ = std::make_unique<Pipeline::IQAcquisitionBridge>(100);
  demodNode_ = std::make_unique<Pipeline::IQDemodulationNode>();
  demodPassthrough_ = std::make_unique<Pipeline::IQDemodulationLink>();
  lineDetectionNode_ = std::make_unique<Pipeline::LineDetectionNode>(sampleRate_);
  lineDetectionPassthrough_ = std::make_unique<Pipeline::LineDetectionLink>();
  frameCompositionNode_ = std::make_unique<Pipeline::FrameCompositionNode>(sampleRate_);
  frameCompositionPassthrough_ = std::make_unique<Pipeline::FrameCompositionLink>();
  outputQueueNode_ = std::make_unique<Pipeline::OutputQueueNode>(30, [this] () {
    if (!running_.load(std::memory_order_acquire)) return false;
    onFrameAvailable_();
    return true;
  });

  connectPipelineNodes();

  return true;
}

void VideoProcessor::connectPipelineNodes() {
  // Acquisition node reads from the stream
  acquisitionNode_->sendDataTo(acquisitionBridge_.get());

  demodNode_->receiveDataFrom(acquisitionBridge_.get());
  demodNode_->sendDataTo(demodPassthrough_.get());

  lineDetectionNode_->receiveDataFrom(demodPassthrough_.get());
  lineDetectionNode_->sendDataTo(lineDetectionPassthrough_.get());

  frameCompositionNode_->receiveDataFrom(lineDetectionPassthrough_.get());
  frameCompositionNode_->sendDataTo(frameCompositionPassthrough_.get());

  outputQueueNode_->receiveDataFrom(frameCompositionPassthrough_.get());
}

void VideoProcessor::deinitialize() {
  acquisitionNode_.reset();
  acquisitionBridge_.reset();
  demodNode_.reset();
  demodPassthrough_.reset();
  lineDetectionNode_.reset();
  lineDetectionPassthrough_.reset();
  frameCompositionNode_.reset();
  frameCompositionPassthrough_.reset();
  outputQueueNode_.reset();
  acquisitionThread_.reset();
  pipelineMainThread_.reset();
  stream_.reset();
  sampleRate_ = 0;
  pipelineThreadRunning_.store(false, std::memory_order_release);
}

void VideoProcessor::sendStopToPipeline() const {
  if (acquisitionNode_) acquisitionNode_->stop();
  if (acquisitionBridge_) acquisitionBridge_->stop();
  if (demodNode_) demodNode_->stop();
  if (demodPassthrough_) demodPassthrough_->stop();
  if (lineDetectionNode_) lineDetectionNode_->stop();
  if (lineDetectionPassthrough_) lineDetectionPassthrough_->stop();
  if (frameCompositionNode_) frameCompositionNode_->stop();
  if (frameCompositionPassthrough_) frameCompositionPassthrough_->stop();
  if (outputQueueNode_) outputQueueNode_->stop();
}

} // namespace IQVideoProcessor
