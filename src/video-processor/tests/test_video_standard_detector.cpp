#include "../pipeline/line-detection-node/partials/video_standard_detector.h"
#include <cassert>
#include <iostream>
#include <vector>
#include <cmath>
#include <chrono>
#include <cstdlib>

namespace VideoStandardDetectorTests {

using namespace IQVideoProcessor;

// Test helper functions
std::vector<VideoStandardDetector::SyncPulseData> generateNTSCSyncPattern(
    SampleRateType sampleRate, TFloat startPosition, uint32_t frameCount) {
    
    std::vector<VideoStandardDetector::SyncPulseData> pulses;
    
    // NTSC: 29.97 fps, 525 lines per frame, 63.5 μs per line
    const TFloat frameRate = 29.97f;
    const TFloat lineDurationSamples = static_cast<TFloat>(sampleRate) * 63.5e-6f;
    const TFloat frameDurationSamples = static_cast<TFloat>(sampleRate) / frameRate;
    const uint32_t linesPerFrame = 525;
    
    TFloat currentPosition = startPosition;
    
    for (uint32_t frame = 0; frame < frameCount; ++frame) {
        TFloat frameStartPosition = currentPosition;

        // Add vertical sync sequence at frame start (more realistic pattern)
        // Pre-equalizing pulses
        for (int i = 0; i < 6; ++i) {
            pulses.emplace_back(EQUALIZING_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Vertical sync pulses
        for (int i = 0; i < 6; ++i) {
            pulses.emplace_back(VERTICAL_SYNC_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Post-equalizing pulses
        for (int i = 0; i < 6; ++i) {
            pulses.emplace_back(EQUALIZING_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Add horizontal sync pulses for the rest of the frame
        uint32_t remainingLines = linesPerFrame - 18; // Account for vertical sync sequence
        for (uint32_t line = 0; line < remainingLines; ++line) {
            pulses.emplace_back(HORIZONTAL_SYNC_PULSE, currentPosition);
            currentPosition += lineDurationSamples;
        }

        // Ensure frame duration is correct
        currentPosition = frameStartPosition + frameDurationSamples;
    }
    
    return pulses;
}

std::vector<VideoStandardDetector::SyncPulseData> generatePALSyncPattern(
    SampleRateType sampleRate, TFloat startPosition, uint32_t frameCount) {
    
    std::vector<VideoStandardDetector::SyncPulseData> pulses;
    
    // PAL: 25 fps, 625 lines per frame, 64 μs per line
    const TFloat frameRate = 25.0f;
    const TFloat lineDurationSamples = static_cast<TFloat>(sampleRate) * 64.0e-6f;
    const TFloat frameDurationSamples = static_cast<TFloat>(sampleRate) / frameRate;
    const uint32_t linesPerFrame = 625;
    
    TFloat currentPosition = startPosition;
    
    for (uint32_t frame = 0; frame < frameCount; ++frame) {
        TFloat frameStartPosition = currentPosition;

        // Add vertical sync sequence at frame start (PAL pattern)
        // Pre-equalizing pulses
        for (int i = 0; i < 5; ++i) {
            pulses.emplace_back(EQUALIZING_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Vertical sync pulses
        for (int i = 0; i < 5; ++i) {
            pulses.emplace_back(VERTICAL_SYNC_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Post-equalizing pulses
        for (int i = 0; i < 5; ++i) {
            pulses.emplace_back(EQUALIZING_PULSE, currentPosition);
            currentPosition += lineDurationSamples * 0.5f;
        }

        // Add horizontal sync pulses for the rest of the frame
        uint32_t remainingLines = linesPerFrame - 15; // Account for vertical sync sequence
        for (uint32_t line = 0; line < remainingLines; ++line) {
            pulses.emplace_back(HORIZONTAL_SYNC_PULSE, currentPosition);
            currentPosition += lineDurationSamples;
        }

        // Ensure frame duration is correct
        currentPosition = frameStartPosition + frameDurationSamples;
    }
    
    return pulses;
}

std::vector<VideoStandardDetector::SyncPulseData> generateNoisySyncPattern(
    const std::vector<VideoStandardDetector::SyncPulseData>& cleanPattern, 
    TFloat noiseLevel) {
    
    std::vector<VideoStandardDetector::SyncPulseData> noisyPattern = cleanPattern;
    
    // Add timing noise to positions
    for (auto& pulse : noisyPattern) {
        TFloat noise = (static_cast<TFloat>(rand()) / RAND_MAX - 0.5f) * 2.0f * noiseLevel;
        pulse.centerPosition += noise;
    }
    
    // Randomly change some pulse types (simulate detection errors)
    for (uint32_t i = 0; i < noisyPattern.size(); ++i) {
        if ((rand() % 100) < 5) { // 5% chance of misdetection
            noisyPattern[i].type = UNKNOWN;
        }
    }
    
    return noisyPattern;
}

void test_basic_construction() {
    std::cout << "Testing VideoStandardDetector basic construction..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    const auto& result = detector.getCurrentResult();
    assert(result.standard == STANDARD_UNKNOWN);
    assert(result.status == DETECTION_IN_PROGRESS);
    assert(result.frameRate == 0.0f);
    assert(result.confidenceLevel == 0.0f);
    
    std::cout << "✓ Basic construction test passed" << std::endl;
}

void test_ntsc_detection() {
    std::cout << "Testing NTSC standard detection..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // Generate NTSC sync pattern for 5 frames
    auto syncPulses = generateNTSCSyncPattern(sampleRate, 0.0f, 5);
    
    auto result = detector.processSyncPulses(syncPulses);

    // Should detect NTSC with high confidence
    assert(result.standard == NTSC);
    assert(result.status == DETECTION_COMPLETE);
    assert(result.confidenceLevel > 0.8f);
    assert(std::abs(result.frameRate - 29.97f) < 1.0f);
    assert(result.linesPerFrame > 500 && result.linesPerFrame < 550);
    assert(result.interlaced == true);
    
    std::cout << "✓ NTSC detection test passed" << std::endl;
    std::cout << "  Detected frame rate: " << result.frameRate << " fps" << std::endl;
    std::cout << "  Confidence level: " << result.confidenceLevel << std::endl;
}

void test_pal_detection() {
    std::cout << "Testing PAL standard detection..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // Generate PAL sync pattern for 5 frames
    auto syncPulses = generatePALSyncPattern(sampleRate, 0.0f, 5);
    
    auto result = detector.processSyncPulses(syncPulses);
    
    // Should detect PAL with high confidence
    assert(result.standard == PAL);
    assert(result.status == DETECTION_COMPLETE);
    assert(result.confidenceLevel > 0.8f);
    assert(std::abs(result.frameRate - 25.0f) < 1.0f);
    assert(result.linesPerFrame > 600 && result.linesPerFrame < 650);
    assert(result.interlaced == true);
    
    std::cout << "✓ PAL detection test passed" << std::endl;
    std::cout << "  Detected frame rate: " << result.frameRate << " fps" << std::endl;
    std::cout << "  Confidence level: " << result.confidenceLevel << std::endl;
}

void test_progressive_detection() {
    std::cout << "Testing progressive detection with multiple batches..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // Generate NTSC pattern and split into batches
    auto fullPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 6);
    
    // Process in smaller batches
    uint32_t batchSize = fullPattern.size() / 3;
    
    // First batch - should be in progress
    std::vector<VideoStandardDetector::SyncPulseData> batch1(
        fullPattern.begin(), fullPattern.begin() + batchSize);
    auto result1 = detector.processSyncPulses(batch1);
    assert(result1.status == DETECTION_IN_PROGRESS);
    
    // Second batch - might still be in progress
    std::vector<VideoStandardDetector::SyncPulseData> batch2(
        fullPattern.begin() + batchSize, fullPattern.begin() + 2 * batchSize);
    auto result2 = detector.processSyncPulses(batch2);
    
    // Third batch - should complete detection
    std::vector<VideoStandardDetector::SyncPulseData> batch3(
        fullPattern.begin() + 2 * batchSize, fullPattern.end());
    auto result3 = detector.processSyncPulses(batch3);
    
    assert(result3.standard == NTSC);
    assert(result3.status == DETECTION_COMPLETE);
    assert(result3.confidenceLevel > 0.7f);
    
    std::cout << "✓ Progressive detection test passed" << std::endl;
}

void test_noisy_signal_detection() {
    std::cout << "Testing detection with noisy signals..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // Generate clean NTSC pattern
    auto cleanPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 8);
    
    // Add light noise (±5 samples timing jitter)
    auto noisyPattern = generateNoisySyncPattern(cleanPattern, 5.0f);
    
    auto result = detector.processSyncPulses(noisyPattern);
    
    // Should still detect NTSC but with lower confidence, or might fail with heavy noise
    if (result.status == DETECTION_COMPLETE) {
        assert(result.standard == NTSC);
        assert(result.confidenceLevel > 0.3f); // Very low threshold for noisy signals
    } else {
        // Heavy noise might cause detection to fail, which is acceptable
        assert(result.status == DETECTION_FAILED || result.status == DETECTION_IN_PROGRESS);
    }
    
    std::cout << "✓ Noisy signal detection test passed" << std::endl;
    std::cout << "  Confidence with noise: " << result.confidenceLevel << std::endl;
}

void test_insufficient_data() {
    std::cout << "Testing behavior with insufficient data..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // Generate very small pattern (less than minimum frames)
    auto smallPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 1);
    
    auto result = detector.processSyncPulses(smallPattern);
    
    // Should remain in progress
    assert(result.status == DETECTION_IN_PROGRESS);
    assert(result.standard == STANDARD_UNKNOWN);
    
    std::cout << "✓ Insufficient data test passed" << std::endl;
}

void test_reset_functionality() {
    std::cout << "Testing detector reset functionality..." << std::endl;
    
    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);
    
    // First detection
    auto ntscPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 5);
    auto result1 = detector.processSyncPulses(ntscPattern);
    assert(result1.standard == NTSC);
    assert(result1.status == DETECTION_COMPLETE);
    
    // Reset detector
    detector.reset();
    auto resetResult = detector.getCurrentResult();
    assert(resetResult.status == DETECTION_IN_PROGRESS);
    assert(resetResult.standard == STANDARD_UNKNOWN);
    
    // Second detection with PAL
    auto palPattern = generatePALSyncPattern(sampleRate, 0.0f, 5);
    auto result2 = detector.processSyncPulses(palPattern);
    assert(result2.standard == PAL);
    assert(result2.status == DETECTION_COMPLETE);
    
    std::cout << "✓ Reset functionality test passed" << std::endl;
}

void test_edge_cases() {
    std::cout << "Testing edge cases..." << std::endl;

    SampleRateType sampleRate = 20000000; // 20 MHz

    // Test with empty pulse vector
    {
        VideoStandardDetector detector(sampleRate);
        std::vector<VideoStandardDetector::SyncPulseData> emptyPulses;
        auto result = detector.processSyncPulses(emptyPulses);
        assert(result.status == DETECTION_IN_PROGRESS);
    }

    // Test with only unknown pulses
    {
        VideoStandardDetector detector(sampleRate);
        std::vector<VideoStandardDetector::SyncPulseData> unknownPulses;
        for (int i = 0; i < 100; ++i) {
            unknownPulses.emplace_back(UNKNOWN, static_cast<TFloat>(i * 1000));
        }
        auto result = detector.processSyncPulses(unknownPulses);
        assert(result.status == DETECTION_IN_PROGRESS || result.status == DETECTION_FAILED);
    }

    // Test with mixed standards (should fail or have low confidence)
    {
        VideoStandardDetector detector(sampleRate);
        auto ntscPart = generateNTSCSyncPattern(sampleRate, 0.0f, 2);
        auto palPart = generatePALSyncPattern(sampleRate, 100000.0f, 2);

        std::vector<VideoStandardDetector::SyncPulseData> mixedPattern;
        mixedPattern.insert(mixedPattern.end(), ntscPart.begin(), ntscPart.end());
        mixedPattern.insert(mixedPattern.end(), palPart.begin(), palPart.end());

        auto result = detector.processSyncPulses(mixedPattern);
        // Should either fail or have very low confidence
        if (result.status == DETECTION_COMPLETE) {
            assert(result.confidenceLevel < 0.6f);
        }
    }

    std::cout << "✓ Edge cases test passed" << std::endl;
}

void test_custom_configuration() {
    std::cout << "Testing custom configuration..." << std::endl;

    SampleRateType sampleRate = 20000000; // 20 MHz

    // Test with stricter confidence requirements
    VideoStandardDetector::Config strictConfig = VideoStandardDetector::getDefaultConfig();
    strictConfig.minConfidenceLevel = 0.95f;
    strictConfig.tolerancePercent = 1.0f; // Very tight tolerance

    VideoStandardDetector strictDetector(sampleRate, strictConfig);

    // Generate perfect NTSC pattern
    auto perfectPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 8);
    auto result = strictDetector.processSyncPulses(perfectPattern);

    // Should still detect but might require more frames
    assert(result.standard == NTSC || result.status == DETECTION_IN_PROGRESS);

    // Test with very loose configuration
    VideoStandardDetector::Config looseConfig = VideoStandardDetector::getDefaultConfig();
    looseConfig.minConfidenceLevel = 0.3f;
    looseConfig.tolerancePercent = 20.0f; // Very loose tolerance
    looseConfig.minFramesForDetection = 1.0f;

    VideoStandardDetector looseDetector(sampleRate, looseConfig);

    // Should detect even with minimal data
    auto minimalPattern = generateNTSCSyncPattern(sampleRate, 0.0f, 2);
    auto looseResult = looseDetector.processSyncPulses(minimalPattern);

    assert(looseResult.status == DETECTION_COMPLETE);
    assert(looseResult.standard == NTSC);

    std::cout << "✓ Custom configuration test passed" << std::endl;
}

void test_performance_stress() {
    std::cout << "Testing performance with large datasets..." << std::endl;

    SampleRateType sampleRate = 20000000; // 20 MHz
    VideoStandardDetector detector(sampleRate);

    // Generate large NTSC pattern (50 frames)
    auto largePattern = generateNTSCSyncPattern(sampleRate, 0.0f, 50);

    auto startTime = std::chrono::high_resolution_clock::now();
    auto result = detector.processSyncPulses(largePattern);
    auto endTime = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    assert(result.standard == NTSC);
    assert(result.status == DETECTION_COMPLETE);
    assert(result.confidenceLevel > 0.8f);

    std::cout << "✓ Performance stress test passed" << std::endl;
    std::cout << "  Processing time for " << largePattern.size() << " pulses: "
              << duration.count() << " ms" << std::endl;
}

void run_all_tests() {
    std::cout << "Running VideoStandardDetector Tests" << std::endl;
    std::cout << "====================================" << std::endl;

    try {
        test_basic_construction();
        test_ntsc_detection();
        test_pal_detection();
        test_progressive_detection();
        test_noisy_signal_detection();
        test_insufficient_data();
        test_reset_functionality();
        test_edge_cases();
        test_custom_configuration();
        test_performance_stress();

        std::cout << std::endl;
        std::cout << "🎉 All VideoStandardDetector tests passed!" << std::endl;
        std::cout << "✅ Basic functionality" << std::endl;
        std::cout << "✅ NTSC detection" << std::endl;
        std::cout << "✅ PAL detection" << std::endl;
        std::cout << "✅ Progressive detection" << std::endl;
        std::cout << "✅ Noise tolerance" << std::endl;
        std::cout << "✅ Edge cases" << std::endl;
        std::cout << "✅ Configuration flexibility" << std::endl;
        std::cout << "✅ Performance under load" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        throw;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        throw;
    }
}

} // namespace VideoStandardDetectorTests

// Test runner function for integration with main test suite
int run_video_standard_detector_tests() {
    try {
        VideoStandardDetectorTests::run_all_tests();
        return 0; // Success
    } catch (const std::exception& e) {
        std::cerr << "VideoStandardDetector tests failed with exception: " << e.what() << std::endl;
        return 1; // Failure
    } catch (...) {
        std::cerr << "VideoStandardDetector tests failed with unknown exception" << std::endl;
        return 1; // Failure
    }
}
