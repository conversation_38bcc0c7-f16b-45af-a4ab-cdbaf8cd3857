#pragma once
#include <cstdint>
#include <cstddef>
#include "../types.h"
#include "./video_standard.h"

template <typename T> constexpr T cmax(T v) { return v; }
template <typename T, typename... Ts> constexpr T cmax(T v, Ts... vs) {
  const T tail = cmax(vs...);
  return v < tail ? tail : v;
}

// IQ Stream read / buffering configs
constexpr uint32_t IQ_STREAM_READ_SIZE                    = 8 * 1024;         // Reading the IQ stream in chunks of 8k samples
constexpr uint32_t IQ_STREAM_MIN_READ_BUFF_NUM            = 64;               // Minimum number of read buffers for IQ stream acquisition

// IQ Stream chunk configs
constexpr double MIN_LINE_RATE_HZ                         = 15000.0;          // Minimum line rate for video processing (auto)
constexpr uint32_t MIN_SAMPLES_PER_VIDEO_LINE             = 320;              // Minimum samples per video line for processing (topic for discussion)

// Number of video lines per processing chunk (controls per-window effectiveSamples)
constexpr double LINES_PER_CHUNK                          = 100.0;            // Put 100 lines per chunk for processing

// Sync detector filter configs, used in line detection node to pre-filter the signal for better sync pulse detection
constexpr uint32_t SYNC_DETECTOR_FILTER_HZ                = 500e3;            // 500 kHz, sync detector filter frequency

// Video sync pulses detection configs
constexpr float H_OR_EQ_PULSE_MIN_WIDTH_SEC             = 1.5e-6;          // Minimum width of horizontal or equalizing pulse in seconds
constexpr float H_OR_EQ_PULSE_MAX_WIDTH_SEC             = 7e-6;            // Maximum width of horizontal or equalizing pulse in seconds
constexpr float V_SYNC_PULSE_MIN_WIDTH_SEC              = 26e-6;           // Minimum width of vertical sync pulse in seconds
constexpr float V_SYNC_PULSE_MAX_WIDTH_SEC              = 30e-6;           // Maximum width of vertical sync pulse in seconds
constexpr float V_SYNC_LONG_PULSE_MIN_WIDTH_SEC         = 440e-6;          // Minimum width of long vertical sync pulse in seconds
constexpr float V_SYNC_LONG_PULSE_MAX_WIDTH_SEC         = 500e-6;          // Maximum width of long vertical sync pulse in seconds
// Used in line detection node to initialize the maximum expected width of any sync pulse
constexpr float PULSE_MAX_WIDTH_US = cmax(
  H_OR_EQ_PULSE_MIN_WIDTH_SEC,
  H_OR_EQ_PULSE_MAX_WIDTH_SEC,
  V_SYNC_PULSE_MIN_WIDTH_SEC,
  V_SYNC_PULSE_MAX_WIDTH_SEC,
  V_SYNC_LONG_PULSE_MIN_WIDTH_SEC,
  V_SYNC_LONG_PULSE_MAX_WIDTH_SEC
);

constexpr double H_LINE_DISTANCE_MIN_SEC                = 62.3e-6;         // Minimum horizontal line distance in seconds
constexpr double H_LINE_DISTANCE_MAX_SEC                = 65.2e-6;         // Maximum horizontal line distance in seconds
constexpr double H_LINE_HALF_DISTANCE_MIN_SEC           = 30.0e-6;         // Minimum half horizontal line distance in seconds
constexpr double H_LINE_HALF_DISTANCE_MAX_SEC           = 35.0e-6;         // Maximum half horizontal line distance in seconds
constexpr double H_LINE_70PERCENT_DISTANCE_MIN_SEC      = 42.0e-6;         // Minimum 70% horizontal line distance in seconds
constexpr double H_LINE_70PERCENT_DISTANCE_MAX_SEC      = 46.0e-6;         // Maximum 70% horizontal line distance in seconds
constexpr double H_LINE_33PERCENT_DISTANCE_MIN_SEC      = 18.0e-6;         // Minimum 33% horizontal line distance in seconds
constexpr double H_LINE_33PERCENT_DISTANCE_MAX_SEC      = 22.0e-6;         // Maximum 33% horizontal line distance in seconds

constexpr double EQUALIZATION_BLACK_LEVEL_REGION_SEC    = 10.0e-6;         // Equalization black level calculation region size in seconds
constexpr double EQUALIZATION_BLACK_LEVEL_OFFSET_SEC    = 7.5e-6;          // Equalization black level calculation region offset in seconds

namespace IQVideoProcessor::Pipeline {

struct WhiteLevelCalibratorSettings;

const WhiteLevelCalibratorSettings& getWhiteLevelCalibratorDefaults();
float getVideoStandardFrameRate(IQVideoProcessor::VideoStandard standard);

} // namespace IQVideoProcessor::Pipeline
