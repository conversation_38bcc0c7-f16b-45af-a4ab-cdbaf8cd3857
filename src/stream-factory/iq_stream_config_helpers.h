#pragma once
#include <v8.h>
#include <string>
#include <tuple>
#include "../wav-stream/wav_stream.h"
#include "../bladerf-stream/bladerf_stream.h"

namespace IQStreamConfigHelpers {

struct WAVStreamConfig {
  std::string path;
  bool loop = true;
  bool simulateTiming = true;
};

struct BladeRFStreamConfig {
  double frequencyHz = 915e6;
  double sampleRateHz = 1.0e6;
  double bandwidthHz = 1.5e6;
  int channel = 0;
  BladeRFIQStream::GainMode gainMode = BladeRFIQStream::GainMode::MANUAL;
  int manualGainDb = 30;
};

// Forward conversion (JS → C++)
std::tuple<WAVStreamConfig, std::string> convertJSToWAVConfig(v8::Isolate* isolate, const v8::Local<v8::Object>& jsConfig);
std::tuple<BladeRFStreamConfig, std::string> convertJSToBladeRFConfig(v8::Isolate* isolate, const v8::Local<v8::Object>& jsConfig);

// Backward conversion (C++ → JS)
v8::Local<v8::Object> convertWAVConfigToJS(v8::Isolate* isolate, const WAVStreamConfig& config, SampleRate actualSampleRate);
v8::Local<v8::Object> convertBladeRFConfigToJS(v8::Isolate* isolate, const BladeRFStreamConfig& config, const BladeRFIQStream::ResolvedInfo& resolvedInfo);

}
