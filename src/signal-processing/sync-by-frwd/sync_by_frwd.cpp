#include "sync_by_frwd.h"
#include <cassert>
#include <cmath>

namespace IQVideoProcessor::SignalProcessing {

using std::abs;
using std::ceil;

SyncByFrwd::SyncByFrwd(const VSampleFloat* data, const uint32_t dataSize)
  : data_(data), dataSize_(dataSize) {
  // Allow empty buffers: data_ may be null when dataSize_ == 0
  assert((dataSize_ == 0) || (data_ != nullptr));
}

std::optional<VSampleFloat> SyncByFrwd::operator()(
  const bool toEOF,
  const int32_t frontType,
  const VSampleFloat searchedValue,
  const float fromPosition,
  const float samples,
  const float fThresholdSize,
  const VSampleFloat thresholdTrigDelta
) const {
  // Simplified parameter derivation - no averaging logic needed
  // aveSize is always 1, so no odd forcing or halfAveSize calculation
  const uint32_t thresholdSize = fThresholdSize <= static_cast<float>(1) ? 1 : static_cast<uint32_t>(fThresholdSize + static_cast<float>(0.5));

  float endPosition = fromPosition + samples;
  const auto processingStartPos = static_cast<uint32_t>(fromPosition);

  uint32_t processingSize;
  if (toEOF) {
    processingSize = (processingStartPos < dataSize_) ? (dataSize_ - processingStartPos) : 0u;
    endPosition = static_cast<float>(dataSize_ - 1); // to EOF means to the last element
  } else if (samples <= static_cast<float>(0)) {
    processingSize = 0u;
  } else {
    processingSize = static_cast<uint32_t>(ceil(endPosition)) - processingStartPos;
    if (processingStartPos + processingSize > dataSize_) processingSize = dataSize_ - processingStartPos;
  }
  if (processingSize == 0 || processingSize <= thresholdSize) return std::nullopt; // nothing to process, but not an error

  const VSampleFloat* const data = data_ + processingStartPos; // base pointer for processing

  const VSampleFloat searchedDelta = abs(thresholdTrigDelta);
  
  // Process window setup
  const uint32_t unprocessed = processingSize - thresholdSize;

  // Simplified main processing loop - no averaging case only
  uint32_t processed = 0;
  uint32_t prevValueIdx = 0;
  uint32_t nextValueIdx = thresholdSize;

  while (processed < unprocessed) {
    const VSampleFloat prevValue = data[prevValueIdx];
    const VSampleFloat nextValue = data[nextValueIdx];

    // Check for front detection
    VSampleFloat delta = abs(nextValue - prevValue);
    if (delta >= searchedDelta) {
      bool validFrontAndCrossing = false;
      if (frontType > 0) {
        validFrontAndCrossing = (prevValue <= nextValue) && (prevValue <= searchedValue && searchedValue <= nextValue);
      } else if (frontType < 0) {
        validFrontAndCrossing = (prevValue >= nextValue) && (prevValue >= searchedValue && searchedValue >= nextValue);
      } else {
        const bool risingWithCrossing = (prevValue <= nextValue) && (prevValue <= searchedValue && searchedValue <= nextValue);
        const bool fallingWithCrossing = (prevValue >= nextValue) && (prevValue >= searchedValue && searchedValue >= nextValue);
        validFrontAndCrossing = risingWithCrossing || fallingWithCrossing;
      }

      if (validFrontAndCrossing) {
        const auto valuePosition = findPreciseValueLocation(&data[prevValueIdx], thresholdSize, searchedValue, frontType);
        const auto calculatedPos = static_cast<float>(processingStartPos + processed) + valuePosition;
        if (calculatedPos > fromPosition && calculatedPos <= endPosition) {
          return calculatedPos;
        }
      }
    }

    ++prevValueIdx;
    ++nextValueIdx;
    ++processed;
  }

  return std::nullopt;
}

float SyncByFrwd::findPreciseValueLocation(
  const VSampleFloat* data,
  const uint32_t thresholdSize,
  const VSampleFloat searchedValue,
  const int32_t frontType
) const {
  // Optimize for performance: use separate loops for different front types
  if (frontType > 0) {
    for (uint32_t i = 0; i < thresholdSize; ++i) {
      const auto val1 = data[i];
      const auto val2 = data[i + 1];

      if (val1 <= searchedValue && searchedValue <= val2) {
        const auto fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<float>(i) + fraction;
      }
    }
  } else if (frontType < 0) {
    for (uint32_t i = 0; i < thresholdSize; ++i) {
      const auto val1 = data[i];
      const auto val2 = data[i + 1];

      if (val1 >= searchedValue && searchedValue >= val2) {
        const auto fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<float>(i) + fraction;
      }
    }
  } else {
    for (uint32_t i = 0; i < thresholdSize; ++i) {
      const auto val1 = data[i];
      const auto val2 = data[i + 1];

      const bool risingCrossing = (val1 <= searchedValue && searchedValue <= val2);
      const bool fallingCrossing = (val1 >= searchedValue && searchedValue >= val2);

      if (risingCrossing || fallingCrossing) {
        const auto fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<float>(i) + fraction;
      }
    }
  }

  return static_cast<float>(thresholdSize) / static_cast<float>(2); // Just in case, should never happen
}

inline float SyncByFrwd::interpolatePosition(
  const VSampleFloat val1,
  const VSampleFloat val2,
  const VSampleFloat searchedValue
) {
  if (searchedValue == val1) return static_cast<float>(0); // Start of interval
  if (searchedValue == val2) return static_cast<float>(1); // End of interval
  return (searchedValue - val1) / (val2 - val1);
}

}
