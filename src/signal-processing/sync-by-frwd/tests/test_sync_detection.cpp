#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <random>
#include "../sync_by_frwd.h"

using IQVideoProcessor::SignalProcessing::SyncByFrwd;

// Test result tracking
struct FrontTestResult {
  std::string testName;
  bool passed;
  std::string error;
};

std::vector<FrontTestResult> frontResults;

// Helper functions
static std::vector<float> makeStep(uint32_t total, uint32_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (uint32_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static void buildPadded(const std::vector<float>& effective, uint32_t leftPad, uint32_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

// Test execution helper
#define RUN_FRONT_TEST(test_func) do { \
  try { \
    test_func(); \
    frontResults.push_back({#test_func, true, ""}); \
  } catch (const std::exception& e) { \
    frontResults.push_back({#test_func, false, e.what()}); \
  } \
} while(0)

void test_sharp_step_fronts() {
  const uint32_t total = 256, pad = 64;
  
  // Test sharp rising step
  {
    auto effective = makeStep(total, 128, -1.0f, 1.0f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    for (uint32_t thresholdSize : {3, 7, 15, 31}) {
      SyncByFrwd<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.5f, pos, found);

      if (!ok || !found) {
        std::ostringstream oss;
        oss << "Sharp rising step failed with thresholdSize=" << thresholdSize;
        throw std::runtime_error(oss.str());
      }

      // Verify position accuracy
      if (std::abs(pos - 128.0f) > static_cast<float>(thresholdSize) / 2.0f + 2.0f) {
        std::ostringstream oss;
        oss << "Sharp step position inaccurate: expected ~128, got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }

  std::cout << "✓ sharp step fronts" << std::endl;
}

void test_small_amplitude_steps() {
  const uint32_t total = 200, pad = 50;

  // Test detection of very small amplitude changes
  for (float amplitude : {0.01f, 0.05f, 0.1f, 0.2f}) {
    auto effective = makeStep(total, 100, 0.0f, amplitude);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    // Use small threshold sizes for maximum sensitivity
    for (uint32_t thresholdSize : {2, 3, 5, 7}) {
      SyncByFrwd<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      // Use very small threshold for tiny amplitudes, search for value at amplitude/2
      float threshold = amplitude * 0.3f;
      float searchedValue = amplitude * 0.5f;
      bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, static_cast<float>(thresholdSize), threshold, pos, found);

      if (!ok) {
        std::ostringstream oss;
        oss << "Small amplitude precision test failed for amplitude " << amplitude
            << " with thresholdSize " << thresholdSize;
        throw std::runtime_error(oss.str());
      }
    }
  }

  std::cout << "✓ small amplitude steps" << std::endl;
}

void test_noisy_step_fronts() {
  const uint32_t total = 300, pad = 75;
  std::random_device rd;
  std::mt19937 gen(42); // Fixed seed for reproducibility

  for (float noiseLevel : {0.01f, 0.05f, 0.1f}) {
    auto effective = makeStep(total, 150, -0.5f, 0.5f);
    
    // Add noise
    std::normal_distribution<float> noise(0.0f, noiseLevel);
    for (auto& val : effective) {
      val += noise(gen);
    }
    
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    // Test with various threshold sizes
    for (uint32_t thresholdSize : {3, 7, 15}) {
      SyncByFrwd<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      // Use higher threshold to overcome noise, search for mid-range value
      float threshold = 0.5f + noiseLevel * 10.0f;
      float searchedValue = 0.0f; // Search for zero crossing
      bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, static_cast<float>(thresholdSize), threshold, pos, found);

      if (!ok) {
        std::ostringstream oss;
        oss << "Noisy signal test failed for noise " << noiseLevel
            << " with thresholdSize " << thresholdSize;
        throw std::runtime_error(oss.str());
      }
    }
  }

  std::cout << "✓ noisy step fronts" << std::endl;
}

void test_front_type_specificity() {
  const uint32_t total = 200, pad = 50;

  // Test that rising front type only detects rising fronts
  {
    auto effective = makeStep(total, 100, 1.0f, -1.0f); // Falling step
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    SyncByFrwd<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    // Ask for rising front (+1) but signal has falling front
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 7.0f, 0.5f, pos, found);

    if (!ok) {
      throw std::runtime_error("Front type specificity test execution failed");
    }
    
    // Should not find rising front in falling signal
    if (found) {
      throw std::runtime_error("Incorrectly detected rising front in falling signal");
    }
  }

  std::cout << "✓ front type specificity" << std::endl;
}

void test_precision_detection() {
  const uint32_t total = 400, pad = 100;

  // Test precision with various step positions
  for (uint32_t stepPos : {150, 200, 250, 300}) {
    auto effective = makeStep(total, stepPos, -0.7f, 0.7f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    for (uint32_t thresholdSize : {5, 11, 21}) {
      SyncByFrwd<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.3f, pos, found);

      if (!ok || !found) {
        std::ostringstream oss;
        oss << "Precision detection failed at stepPos=" << stepPos
            << " with thresholdSize=" << thresholdSize;
        throw std::runtime_error(oss.str());
      }

      // Verify position accuracy
      float expectedPos = static_cast<float>(stepPos);
      float tolerance = static_cast<float>(thresholdSize) / 2.0f + 3.0f;
      if (std::abs(pos - expectedPos) > tolerance) {
        std::ostringstream oss;
        oss << "Position inaccurate at stepPos=" << stepPos
            << ": expected ~" << expectedPos << ", got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }

  std::cout << "✓ precision detection" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Synchronization Detection Test Suite" << std::endl;
  std::cout << "===============================================" << std::endl;

  // Run step function tests
  RUN_FRONT_TEST(test_sharp_step_fronts);
  RUN_FRONT_TEST(test_small_amplitude_steps);

  // Run noisy signal tests
  RUN_FRONT_TEST(test_noisy_step_fronts);

  // Run front type tests
  RUN_FRONT_TEST(test_front_type_specificity);

  // Run precision tests
  RUN_FRONT_TEST(test_precision_detection);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "FRONT DETECTION TEST RESULTS" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  uint32_t passed = 0, failed = 0;
  for (const auto& result : frontResults) {
    if (result.passed) {
      std::cout << "✓ " << result.testName << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.testName << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nTotal: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
