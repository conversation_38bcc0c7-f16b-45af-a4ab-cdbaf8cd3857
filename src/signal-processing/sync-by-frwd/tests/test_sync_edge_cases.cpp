#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <limits>
#include "../sync_by_frwd.h"

using IQVideoProcessor::SignalProcessing::SyncByFrwd;

// Test result tracking
struct EdgeTestResult {
  std::string name;
  bool passed;
  std::string error;
};

std::vector<EdgeTestResult> edgeResults;

// Test execution helper
#define RUN_EDGE_TEST(test_func) do { \
  try { \
    test_func(); \
    edgeResults.push_back({#test_func, true, ""}); \
  } catch (const std::exception& e) { \
    edgeResults.push_back({#test_func, false, e.what()}); \
  } \
} while(0)

void test_identical_values() {
  // Test searching for value between two identical values
  std::vector<float> data = {0.5f, 1.0f, 1.0f, 1.5f};
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  // Search for 1.0 when data contains [1.0, 1.0] at indices 1,2
  bool ok = finder(true, +1, 1.0f, 0.0f, 0.0f, 3.0f, 0.1f, pos, found);

  if (!ok) throw std::runtime_error("execution failed");
  
  // Should find the value at one of the identical positions
  if (found && (pos < 1.0f || pos > 2.0f)) {
    std::ostringstream oss;
    oss << "position out of expected range: got " << pos << ", expected 1.0-2.0";
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ identical values" << std::endl;
}

void test_boundary_conditions() {
  // Test searched value at array start
  {
    std::vector<float> data = {1.0f, 2.0f, 3.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 1.0f, 0.0f, 0.0f, 2.0f, 0.5f, pos, found);
    if (!ok) throw std::runtime_error("boundary start test execution failed");
    
    if (found && std::abs(pos - 0.0f) > 0.1f) {
      std::ostringstream oss;
      oss << "start boundary position incorrect: expected ~0.0, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  // Test searched value at array end
  {
    std::vector<float> data = {1.0f, 2.0f, 3.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 3.0f, 0.0f, 0.0f, 2.0f, 0.5f, pos, found);
    if (!ok) throw std::runtime_error("boundary end test execution failed");
    
    if (found && std::abs(pos - 2.0f) > 0.1f) {
      std::ostringstream oss;
      oss << "end boundary position incorrect: expected ~2.0, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ boundary conditions" << std::endl;
}

void test_precision_edge_cases() {
  // Test very small differences between adjacent values
  std::vector<float> data = {1.0f, 1.0f + 1e-6f, 1.0f + 2e-6f};
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  // Search for value in the middle of tiny difference
  float searchedValue = 1.0f + 1.5e-6f;
  bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, 2.0f, 5e-7f, pos, found);

  if (!ok) throw std::runtime_error("precision test execution failed");
  
  // Should find interpolated position between indices 1 and 2
  if (found && (pos < 1.0f || pos > 2.0f)) {
    std::ostringstream oss;
    oss << "precision position out of range: got " << pos;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ precision edge cases" << std::endl;
}

void test_all_front_type_combinations() {
  // Test rising front
  {
    std::vector<float> data = {-1.0f, 0.0f, 1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 2.0f, 0.3f, pos, found);
    if (!ok || !found) throw std::runtime_error("rising front test failed");
    
    // Should interpolate to position 1.5
    if (std::abs(pos - 1.5f) > 0.1f) {
      std::ostringstream oss;
      oss << "rising front position incorrect: expected ~1.5, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  // Test falling front
  {
    std::vector<float> data = {1.0f, 0.0f, -1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, -1, -0.5f, 0.0f, 0.0f, 2.0f, 0.3f, pos, found);
    if (!ok || !found) throw std::runtime_error("falling front test failed");
    
    // Should interpolate to position 1.5
    if (std::abs(pos - 1.5f) > 0.1f) {
      std::ostringstream oss;
      oss << "falling front position incorrect: expected ~1.5, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  // Test bidirectional with rising pattern
  {
    std::vector<float> data = {-1.0f, 0.0f, 1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, 0, 0.5f, 0.0f, 0.0f, 2.0f, 0.3f, pos, found);
    if (!ok || !found) throw std::runtime_error("bidirectional rising test failed");
  }

  // Test bidirectional with falling pattern
  {
    std::vector<float> data = {1.0f, 0.0f, -1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, 0, -0.5f, 0.0f, 0.0f, 2.0f, 0.3f, pos, found);
    if (!ok || !found) throw std::runtime_error("bidirectional falling test failed");
  }

  std::cout << "✓ all front type combinations" << std::endl;
}

void test_invalid_inputs() {
  // Test empty array
  {
    std::vector<float> data;
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 1.0f, 0.1f, pos, found);
    if (!ok) throw std::runtime_error("empty array should return ok=true");
    if (found) throw std::runtime_error("empty array should not find anything");
  }

  // Test single-element array
  {
    std::vector<float> data = {1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 1.0f, 0.0f, 0.0f, 1.0f, 0.1f, pos, found);
    if (!ok) throw std::runtime_error("single element should return ok=true");
    // Single element arrays cannot have fronts, so found should be false
  }

  std::cout << "✓ invalid inputs" << std::endl;
}

void test_floating_point_precision() {
  // Test with float precision limits
  float epsilon = std::numeric_limits<float>::epsilon();
  std::vector<float> data = {1.0f, 1.0f + epsilon, 1.0f + 2*epsilon};
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  float searchedValue = 1.0f + 1.5f * epsilon;
  bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, 2.0f, epsilon/2, pos, found);

  if (!ok) throw std::runtime_error("floating point precision test execution failed");
  
  std::cout << "✓ floating point precision" << std::endl;
}

void test_multiple_value_occurrences() {
  // Test data where searched value appears multiple times
  std::vector<float> data = {0.0f, 1.0f, 0.5f, 1.0f, 2.0f};
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  // Search for 1.0 which appears at indices 1 and 3
  bool ok = finder(true, +1, 1.0f, 0.0f, 0.0f, 3.0f, 0.2f, pos, found);

  if (!ok) throw std::runtime_error("multiple occurrences test execution failed");
  
  // Should find the first occurrence that satisfies the front type
  if (found && pos < 0.0f) {
    throw std::runtime_error("position should be non-negative");
  }

  std::cout << "✓ multiple value occurrences" << std::endl;
}

void test_interpolation_accuracy() {
  // Test known mathematical interpolation results
  std::vector<float> data = {0.0f, 10.0f, 20.0f};
  SyncByFrwd<float> finder(data.data(), data.size());
  
  // Test interpolation at 25% (should give position 0.25)
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 2.5f, 0.0f, 0.0f, 2.0f, 1.0f, pos, found);
    
    if (!ok || !found) throw std::runtime_error("25% interpolation failed");
    if (std::abs(pos - 0.25f) > 0.01f) {
      std::ostringstream oss;
      oss << "25% interpolation incorrect: expected 0.25, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  // Test interpolation at 75% (should give position 0.75)
  {
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 7.5f, 0.0f, 0.0f, 2.0f, 1.0f, pos, found);
    
    if (!ok || !found) throw std::runtime_error("75% interpolation failed");
    if (std::abs(pos - 0.75f) > 0.01f) {
      std::ostringstream oss;
      oss << "75% interpolation incorrect: expected 0.75, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ interpolation accuracy" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Edge Cases Test Suite" << std::endl;
  std::cout << "=================================" << std::endl;

  // Run all edge case tests
  RUN_EDGE_TEST(test_identical_values);
  RUN_EDGE_TEST(test_boundary_conditions);
  RUN_EDGE_TEST(test_precision_edge_cases);
  RUN_EDGE_TEST(test_all_front_type_combinations);
  RUN_EDGE_TEST(test_invalid_inputs);
  RUN_EDGE_TEST(test_floating_point_precision);
  RUN_EDGE_TEST(test_multiple_value_occurrences);
  RUN_EDGE_TEST(test_interpolation_accuracy);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "EDGE CASES TEST RESULTS" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  uint32_t passed = 0, failed = 0;
  for (const auto& result : edgeResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nTotal: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
