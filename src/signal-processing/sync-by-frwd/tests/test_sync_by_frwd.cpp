#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include "../sync_by_frwd.h"

using IQVideoProcessor::SignalProcessing::SyncByFrwd;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
  double frontPos;
  bool frontFound;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(uint32_t total, uint32_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (uint32_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static void buildPadded(const std::vector<float>& effective, uint32_t leftPad, uint32_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, "", 0.0, false}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what(), 0.0, false}); \
  } \
} while(0)

void test_basic_value_sync() {
  const uint32_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 128, -1.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  // Test basic value-based synchronization
  SyncByFrwd<float> finder(padded.data() + leftPad, total);
  float pos = 0.0f; bool found = false;
  
  // Search for the crossing of value 0.0 (which should be at the step transition)
  bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f, pos, found);
  
  if (!ok || !found) throw std::runtime_error("basic value synchronization failed");
  if (std::abs(pos - 128.0f) > 5.0f) throw std::runtime_error("sync position inaccurate");
  
  std::cout << "✓ basic value synchronization" << std::endl;
}

void test_precise_interpolation() {
  // Create a signal with known interpolation points
  std::vector<float> data = {-1.0f, 0.0f, 1.0f, 2.0f, 3.0f};

  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  // Search for value 0.5 (should be between indices 1 and 2: 0.0 and 1.0)
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);

  if (!ok || !found) throw std::runtime_error("precise interpolation failed");

  // Expected position should be 1.5 (halfway between indices 1 and 2)
  if (std::abs(pos - 1.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "interpolation position incorrect: expected ~1.5, got " << pos;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ precise interpolation" << std::endl;
}

void test_different_searched_values() {
  const uint32_t total = 200, pad = 50;
  auto effective = makeStep(total, 100, -2.0f, 2.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test various searched values within the transition range
  for (float searchedValue : {-1.5f, -1.0f, -0.5f, 0.0f, 0.5f, 1.0f, 1.5f}) {
    SyncByFrwd<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, 7.0f, 1.0f, pos, found);

    if (!ok || !found) {
      std::ostringstream oss;
      oss << "value sync failed for searchedValue=" << searchedValue;
      throw std::runtime_error(oss.str());
    }

    // Position should be near the step transition
    if (std::abs(pos - 100.0f) > 10.0f) {
      std::ostringstream oss;
      oss << "position inaccurate for searchedValue=" << searchedValue
          << ": expected ~100, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ different searched values" << std::endl;
}

void test_front_types() {
  const uint32_t total = 180, pad = 45;

  // Test rising fronts
  {
    auto effective = makeStep(total, 90, -1.0f, 1.0f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    SyncByFrwd<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 7.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("rising front value sync failed");
    }
  }

  // Test falling fronts
  {
    auto effective = makeStep(total, 90, 1.0f, -1.0f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    SyncByFrwd<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, -1, 0.0f, 0.0f, 0.0f, 7.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("falling front value sync failed");
    }
  }

  // Test bidirectional
  {
    auto effective = makeStep(total, 90, -1.0f, 1.0f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    SyncByFrwd<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, 0, 0.0f, 0.0f, 0.0f, 13.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("bidirectional value sync failed");
    }
  }

  std::cout << "✓ front types" << std::endl;
}

void test_value_not_in_window() {
  const uint32_t total = 100, pad = 25;
  auto effective = makeStep(total, 50, -1.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  SyncByFrwd<float> finder(padded.data() + pad, total);
  float pos = 0.0f; bool found = false;

  // Search for a value that's not in the signal range
  bool ok = finder(true, +1, 5.0f, 0.0f, 0.0f, 7.0f, 0.5f, pos, found);

  if (!ok) throw std::runtime_error("execution failed");
  
  // Should not find the value since it's outside the signal range
  if (found) {
    throw std::runtime_error("incorrectly found value outside signal range");
  }

  std::cout << "✓ value not in window" << std::endl;
}

void test_edge_cases() {
  // Test minimal buffer with sufficient threshold size
  {
    std::vector<float> data = {-1.0f, 0.0f, 1.0f};
    SyncByFrwd<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    // Search for 0.0 which is exactly at index 1
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 2.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("minimal buffer value sync failed");
    }
  }

  std::cout << "✓ edge cases" << std::endl;
}

void test_double_precision_instantiation() {
  std::vector<double> data(100, 0.0);
  SyncByFrwd<double> finderD(data.data(), data.size());
  // Just ensure template compiles and can be called with valid inputs
  double posD = 0.0; bool foundD = false;
  bool ok = finderD(true, +1, 0.0, 0.0, 0.0, 33.0, 0.3, posD, foundD);
  (void)ok; (void)foundD;
  std::cout << "✓ double precision instantiation compiled" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Test Suite" << std::endl;
  std::cout << "=====================" << std::endl;

  // Run all tests
  RUN_TEST(test_basic_value_sync);
  RUN_TEST(test_precise_interpolation);
  RUN_TEST(test_different_searched_values);
  RUN_TEST(test_front_types);
  RUN_TEST(test_value_not_in_window);
  RUN_TEST(test_edge_cases);
  RUN_TEST(test_double_precision_instantiation);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "TEST RESULTS SUMMARY" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  uint32_t passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nTotal: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
