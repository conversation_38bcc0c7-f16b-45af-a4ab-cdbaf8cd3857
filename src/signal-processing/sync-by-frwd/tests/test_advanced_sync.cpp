#include "../sync_by_frwd.h"
#include <vector>
#include <iostream>
#include <sstream>
#include <cmath>
#include <algorithm>
#include <random>
#include <chrono>

using namespace IQVideoProcessor::SignalProcessing;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(uint32_t total, uint32_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (uint32_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static std::vector<float> makeSineWave(uint32_t total, float amplitude, float frequency, float offset = 0.0f) {
  std::vector<float> v(total);
  for (uint32_t i = 0; i < total; ++i) {
    v[i] = offset + amplitude * std::sin(2.0f * M_PI * frequency * static_cast<float>(i) / static_cast<float>(total));
  }
  return v;
}

static std::vector<float> makeComplexSignal(uint32_t total) {
  std::vector<float> v(total);
  for (uint32_t i = 0; i < total; ++i) {
    float t = static_cast<float>(i) / static_cast<float>(total);
    // Complex signal with multiple components
    v[i] = 0.5f * std::sin(2.0f * M_PI * 3.0f * t) +
           0.3f * std::sin(2.0f * M_PI * 7.0f * t) +
           0.2f * std::sin(2.0f * M_PI * 11.0f * t);
  }
  return v;
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, ""}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what()}); \
  } \
} while(0)

// Complex waveform synchronization
void test_sine_wave_synchronization() {
  const uint32_t total = 2000;
  auto data = makeSineWave(total, 2.0f, 5.0f); // 5 cycles
  
  SyncByFrwd<float> finder(data.data(), total);
  float pos = 0.0f; bool found = false;
  
  // Look for zero crossing (rising)
  bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 10.0f, 1.0f, pos, found);
  
  if (!ok) {
    throw std::runtime_error("Sine wave sync test execution failed");
  }
  
  // May or may not find zero crossing depending on threshold and delta
  // Sine waves have gradual crossings that may not trigger threshold detection
  
  std::cout << "✓ sine wave synchronization" << std::endl;
}

// Multi-frequency signal synchronization
void test_complex_signal_sync() {
  const uint32_t total = 3000;
  auto data = makeComplexSignal(total);
  
  SyncByFrwd<float> finder(data.data(), total);
  float pos = 0.0f; bool found = false;
  
  // Look for specific value crossing
  bool ok = finder(true, 0, 0.5f, 0.0f, 0.0f, 15.0f, 0.3f, pos, found);
  
  if (!ok) {
    throw std::runtime_error("Complex signal sync test execution failed");
  }
  
  // Complex signals may or may not have crossings that trigger threshold detection
  // Just ensure the algorithm doesn't crash with complex waveforms
  
  std::cout << "✓ complex signal synchronization" << std::endl;
}

// Interpolation precision with known values
void test_interpolation_precision() {
  // Create signal with known precise interpolation points
  std::vector<float> data = {-3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f, 4.0f, 5.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  
  // Test multiple interpolation points
  struct TestCase {
    float searchedValue;
    float expectedPos;
    float tolerance;
  };
  
  std::vector<TestCase> testCases = {
    {-2.5f, 0.5f, 0.05f},   // Between -3 and -2
    {-1.5f, 1.5f, 0.05f},   // Between -2 and -1
    {-0.5f, 2.5f, 0.05f},   // Between -1 and 0
    {0.5f, 3.5f, 0.05f},    // Between 0 and 1
    {1.5f, 4.5f, 0.05f},    // Between 1 and 2
    {2.5f, 5.5f, 0.05f},    // Between 2 and 3
    {3.5f, 6.5f, 0.05f}     // Between 3 and 4
  };
  
  for (const auto& testCase : testCases) {
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, testCase.searchedValue, 0.0f, 0.0f, 4.0f, 0.3f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Interpolation precision test failed for value " << testCase.searchedValue;
      throw std::runtime_error(oss.str());
    }
    
    // Verify interpolation precision
    if (std::abs(pos - testCase.expectedPos) > testCase.tolerance) {
      std::ostringstream oss;
      oss << "Interpolation imprecise for value " << testCase.searchedValue 
          << ": expected " << testCase.expectedPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ interpolation precision" << std::endl;
}

// Large chunk processing with overlaps
void test_large_chunk_overlaps() {
  const uint32_t totalSize = 50000;
  const uint32_t chunkSize = 8192;
  const uint32_t overlapSize = 1024;
  const uint32_t stepPos = 25000;
  
  auto data = makeStep(totalSize, stepPos, -2.0f, 2.0f);
  
  // Process multiple overlapping chunks
  std::vector<std::pair<float, bool>> results;
  
  for (uint32_t chunkStart = 0; chunkStart < totalSize - chunkSize; chunkStart += (chunkSize - overlapSize)) {
    SyncByFrwd<float> finder(data.data() + chunkStart, chunkSize);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 10.0f, 1.0f, pos, found);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Large chunk overlap test failed at chunk start " << chunkStart;
      throw std::runtime_error(oss.str());
    }
    
    if (found) {
      float absolutePos = pos + static_cast<float>(chunkStart);
      results.push_back({absolutePos, found});
    }
  }
  
  // Should find the step in chunks that contain it
  bool foundInCorrectRegion = false;
  for (const auto& result : results) {
    if (result.second && std::abs(result.first - static_cast<float>(stepPos)) < 100.0f) {
      foundInCorrectRegion = true;
      break;
    }
  }
  
  if (!foundInCorrectRegion) {
    throw std::runtime_error("Should find step in correct chunk region");
  }
  
  std::cout << "✓ large chunk overlaps" << std::endl;
}

// Stress test with random searched values
void test_random_searched_values() {
  const uint32_t total = 2000;
  const uint32_t stepPos = 800;
  auto data = makeStep(total, stepPos, -5.0f, 5.0f);
  
  std::mt19937 gen(54321); // Fixed seed
  std::uniform_real_distribution<float> valueDist(-4.0f, 4.0f);
  
  // Test 50 random searched values
  for (int i = 0; i < 50; ++i) {
    float searchedValue = valueDist(gen);
    
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, 8.0f, 2.0f, pos, found);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Random searched value test failed for value " << searchedValue;
      throw std::runtime_error(oss.str());
    }
    
    // If found, position should be reasonable
    if (found && (pos < 700.0f || pos > 900.0f)) {
      std::ostringstream oss;
      oss << "Random value position unreasonable for " << searchedValue << ": " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ random searched values" << std::endl;
}

// Bidirectional front detection accuracy
void test_bidirectional_accuracy() {
  const uint32_t total = 1000;
  
  // Test both rising and falling fronts with bidirectional detection
  std::vector<std::pair<std::string, std::vector<float>>> testCases = {
    {"rising", makeStep(total, 400, -2.0f, 2.0f)},
    {"falling", makeStep(total, 400, 2.0f, -2.0f)}
  };
  
  for (const auto& testCase : testCases) {
    SyncByFrwd<float> finder(testCase.second.data(), total);
    float pos = 0.0f; bool found = false;
    
    // Use bidirectional detection (frontType = 0)
    bool ok = finder(true, 0, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Bidirectional accuracy test failed for " << testCase.first << " front";
      throw std::runtime_error(oss.str());
    }
    
    // Position should be near the step
    if (std::abs(pos - 400.0f) > 10.0f) {
      std::ostringstream oss;
      oss << "Bidirectional position inaccurate for " << testCase.first 
          << ": expected ~400, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ bidirectional accuracy" << std::endl;
}

// Memory and performance stress test
void test_memory_performance_stress() {
  // Test with very large dataset
  const uint32_t totalSize = 10000000; // 10M samples
  const uint32_t stepPos = totalSize / 2;
  
  auto data = makeStep(totalSize, stepPos, -1.0f, 1.0f);
  
  SyncByFrwd<float> finder(data.data(), totalSize);
  float pos = 0.0f; bool found = false;
  
  auto start = std::chrono::high_resolution_clock::now();
  bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 100.0f, 0.5f, pos, found);
  auto end = std::chrono::high_resolution_clock::now();
  
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  
  if (!ok || !found) {
    throw std::runtime_error("Memory performance stress test failed");
  }
  
  // Should complete in reasonable time
  if (duration.count() > 2000) { // 2 seconds max
    std::ostringstream oss;
    oss << "Memory stress test too slow: " << duration.count() << "ms";
    throw std::runtime_error(oss.str());
  }
  
  // Verify position accuracy
  if (std::abs(pos - static_cast<float>(stepPos)) > 200.0f) {
    std::ostringstream oss;
    oss << "Memory stress position inaccurate: expected ~" << stepPos << ", got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ memory performance stress (" << duration.count() << "ms)" << std::endl;
}

// Edge case: value exactly at data boundaries
void test_boundary_value_cases() {
  std::vector<float> data = {-2.0f, -1.0f, 0.0f, 1.0f, 2.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  
  // Test searching for values exactly at data points
  std::vector<float> exactValues = {-2.0f, -1.0f, 0.0f, 1.0f, 2.0f};
  
  for (float exactValue : exactValues) {
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, exactValue, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Boundary value test execution failed for " << exactValue;
      throw std::runtime_error(oss.str());
    }
    
    // Should find exact values (except possibly the first one)
    if (!found && exactValue > -2.0f) {
      std::ostringstream oss;
      oss << "Should find exact value " << exactValue;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ boundary value cases" << std::endl;
}

// Double precision comprehensive test
void test_double_precision_comprehensive() {
  const uint32_t total = 1000;
  const uint32_t stepPos = 500;
  
  std::vector<double> data(total, -1.5);
  for (uint32_t i = stepPos; i < total; ++i) {
    data[i] = 1.5;
  }
  
  SyncByFrwd<double> finder(data.data(), total);
  double pos = 0.0; bool found = false;
  
  // Test with high precision searched value
  bool ok = finder(true, +1, 0.123456789, 0.0, 0.0, 5.0, 1.0, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Double precision comprehensive test failed");
  }
  
  // Verify high precision position
  if (std::abs(pos - static_cast<double>(stepPos)) > 10.0) {
    std::ostringstream oss;
    oss << "Double precision position inaccurate: expected ~" << stepPos << ", got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ double precision comprehensive" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Advanced Test Suite" << std::endl;
  std::cout << "==============================" << std::endl;
  
  // Run all advanced tests
  RUN_TEST(test_sine_wave_synchronization);
  RUN_TEST(test_complex_signal_sync);
  RUN_TEST(test_interpolation_precision);
  RUN_TEST(test_large_chunk_overlaps);
  RUN_TEST(test_random_searched_values);
  RUN_TEST(test_bidirectional_accuracy);
  RUN_TEST(test_memory_performance_stress);
  RUN_TEST(test_boundary_value_cases);
  RUN_TEST(test_double_precision_comprehensive);
  
  // Print results summary
  std::cout << std::endl;
  std::cout << "===================================================" << std::endl;
  std::cout << "ADVANCED SYNC TEST RESULTS" << std::endl;
  std::cout << "===================================================" << std::endl;
  
  int passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }
  
  std::cout << std::endl;
  std::cout << "Total: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;
  
  return failed > 0 ? 1 : 0;
}
