#pragma once
#include <vector>
#include <optional>
#include <cstdint>
#include "../../types.h"

namespace IQVideoProcessor::SignalProcessing {

/**
 * SyncByFrwd - Value-based synchronization algorithm
 *
 * This algorithm extends the simplified front-finding approach with value-based
 * synchronization. It detects fronts and then verifies that the threshold window
 * crosses a specific searched value, providing precise interpolated positioning.
 *
 * Key features:
 * - Front detection with delta threshold validation
 * - Value crossing verification within threshold window
 * - Linear interpolation for precise value location
 * - Support for rising, falling, and bidirectional fronts
 * - No averaging (always behaves as if aveSize=1)
 * - No ring buffer dependency
 */
class SyncByFrwd {
public:
  // Constructor: only needs data pointer and effective length
  explicit SyncByFrwd(const VSampleFloat* data, uint32_t dataSize);

  std::optional<float> operator()(
    bool toEOF, int32_t frontType, VSampleFloat searchedValue, float fromPosition,
    float samples, float fThresholdSize, VSampleFloat thresholdTrigDelta
  ) const;

private:
  const VSampleFloat* data_{nullptr};
  uint32_t dataSize_{0};

  float findPreciseValueLocation(const VSampleFloat* data, uint32_t thresholdSize, VSampleFloat searchedValue, int32_t frontType) const;
  [[nodiscard]] static inline float interpolatePosition(VSampleFloat val1, VSampleFloat val2, VSampleFloat searchedValue) ;
};

} // namespace IQVideoProcessor::SignalProcessing
