{"targets": [{"target_name": "bladerf_addon", "sources": ["src/addon.cpp", "src/video_processor_wrapper.cpp", "src/node/v8_helpers.cpp", "src/node/thread_runner.cpp", "src/signal-processing/sync-by-frwd/sync_by_frwd.cpp", "src/video-processor/video_processor.cpp", "src/video-processor/pipeline/iq-acquisition-node/iq_acquisition_node.cpp", "src/video-processor/pipeline/iq-demodulation-node/iq_demodulation_node.cpp", "src/video-processor/pipeline/line-detection-node/line_detection_node.cpp", "src/video-processor/pipeline/frame-composition-node/frame_composition_node.cpp", "src/video-processor/pipeline/frame-composition-node/partials/frame_canvas.cpp", "src/video-processor/pipeline/output-queue-node/output_queue_node.cpp", "src/video-processor/pipeline/line-detection-node/partials/segment_ave_filter.cpp", "src/video-processor/pipeline/line-detection-node/partials/segment_pulses_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_sync_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/signal_range_estimator.cpp", "src/video-processor/pipeline/line-detection-node/partials/video_standard_detector.cpp", "src/video-processor/pipeline/line-detection-node/partials/sync-orchestrator/sync-orchestrator.cpp", "src/video-processor/pipeline/line-detection-node/partials/sync-orchestrator/sync-encoders/video_sync_encoder.cpp", "src/video-processor/pipeline/line-detection-node/partials/sync-orchestrator/sync-encoders/ntsc_encoder.cpp", "src/video-processor/pipeline/line-detection-node/partials/sync-orchestrator/sync-encoders/pal-encoder.cpp", "src/video-processor/helpers/helpers.cpp", "src/stream-factory/iq_stream_factory.cpp", "src/stream-factory/iq_stream_config_helpers.cpp", "src/wav-stream/wav_stream.cpp", "src/bladerf-stream/bladerf_stream.cpp", "src/logging/logging.cpp"], "include_dirs": ["/usr/local/include", "/usr/include", "/opt/homebrew/include", "/opt/homebrew/Cellar/libbladerf/2023.02_1/include", "src", "src/devtools"], "libraries": ["-lbladeRF", "-lturbojpeg"], "library_dirs": ["/usr/local/lib", "/usr/lib", "/opt/homebrew/lib", "/opt/homebrew/Cellar/libbladerf/2023.02_1/lib"], "cflags_cc": ["-std=c++17", "-fexceptions", "-frtti", "-Wall", "-Wextra"], "cflags": [], "conditions": [["OS=='linux'", {"include_dirs": ["/usr/local/include", "/usr/include", "/usr/include/libbladeRF"], "library_dirs": ["/usr/local/lib", "/usr/lib", "/usr/lib/aarch64-linux-gnu", "/usr/lib/arm-linux-gnueabihf"]}], ["OS=='mac'", {"xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "GCC_ENABLE_CPP_RTTI": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++17", "MACOSX_DEPLOYMENT_TARGET": "10.9", "GCC_INLINES_ARE_PRIVATE_EXTERN": "NO", "GCC_SYMBOLS_PRIVATE_EXTERN": "NO"}}]], "configurations": {"Debug": {"defines": ["DEBUG", "_DEBUG"], "cflags_cc": ["-O0", "-g", "-ggdb3", "-fno-omit-frame-pointer", "-fno-inline-functions", "-fno-optimize-sibling-calls"], "cflags": ["-O0", "-g", "-ggdb3", "-fno-omit-frame-pointer"], "ldflags": ["-g"], "xcode_settings": {"GCC_OPTIMIZATION_LEVEL": "0", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "STRIP_INSTALLED_PRODUCT": "NO", "DEPLOYMENT_POSTPROCESSING": "NO"}}, "Release": {"defines": ["NDEBUG"], "cflags_cc": ["-O3", "-fdata-sections", "-ffunction-sections", "-flto"], "cflags": ["-O3", "-fdata-sections", "-ffunction-sections", "-flto"], "ldflags": ["-O3", "-Wl,--gc-sections", "-flto"], "conditions": [["OS=='linux'", {"conditions": [["target_arch=='arm64'", {"cflags_cc": ["-mcpu=cortex-a76", "-mtune=cortex-a76"], "cflags": ["-mcpu=cortex-a76", "-mtune=cortex-a76"], "ldflags": ["-mcpu=cortex-a76"]}], ["target_arch=='arm'", {"cflags_cc": ["-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard"], "cflags": ["-mcpu=cortex-a53", "-mtune=cortex-a53", "-mfpu=neon-fp-armv8", "-mfloat-abi=hard"], "ldflags": ["-mcpu=cortex-a53", "-mfloat-abi=hard"]}]]}], ["OS=='mac'", {"xcode_settings": {"GCC_OPTIMIZATION_LEVEL": "3", "OTHER_CPLUSPLUSFLAGS": ["-O3", "-flto"], "OTHER_LDFLAGS": ["-O3", "-flto"], "GCC_GENERATE_DEBUGGING_SYMBOLS": "NO", "STRIP_INSTALLED_PRODUCT": "YES", "DEPLOYMENT_POSTPROCESSING": "YES"}}]]}}}]}